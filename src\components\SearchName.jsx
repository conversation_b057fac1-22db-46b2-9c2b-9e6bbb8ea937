import {
  Autocomplete,
  Box,
  Button,
  Grid,
  Paper,
  TextField,
  Typography,
} from "@mui/material";
import _ from "lodash";
import React, { useContext, useState } from "react";
import { useTranslation } from "react-i18next";
import { ReactComponent as FilterIcon } from "@/assets/images/icon_filter.svg";

const SearchName = (props) => {
  const { t } = useTranslation();
  const handleSubmit = () => {
    console.log(111111111);
  };

  const handleReset = () => {};
  console.log(1111111111111);
  return (
    <>
      <Box
        sx={{ display: { xs: "flex", md: "none" }, width: "100%" }}
        flexDirection={"row-reverse"}
      >
        <Box item>
          <FilterIcon width={"35"} height={"35"} className="pointer" />
        </Box>
      </Box>

      <Grid
        container
        my={1}
        p={2}
        component={Paper}
        elevation={0}
        alignItems={"center"}
        className="customAutocomplete"
      >
        <Box>
          <Grid item lg={2} sm={4} xs={12} pr={0.8}>
            <Typography sx={{ fontSize: 13 }}>{t("RTMT0014")}</Typography>
            <TextField size="small" />
          </Grid>

          <Grid container lg={2}>
            <Box
              width={"100%"}
              display={"flex"}
              flexDirection={"row-reverse"}
              pt={1}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-end",
                }}
              >
                <Button
                  variant="contained"
                  sx={{ marginLeft: 1 }}
                  onClick={handleSubmit}
                >
                  {t("LVLGF0009")}
                </Button>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-end",
                }}
                pr={1}
              >
                <Button variant="outlined" onClick={handleReset}>
                  {t("LVLGF0008")}
                </Button>
              </Box>
            </Box>
          </Grid>
        </Box>
      </Grid>
    </>
  );
};

SearchName.defaultProps = {
  onClientChange: () => {},
};

export default SearchName;

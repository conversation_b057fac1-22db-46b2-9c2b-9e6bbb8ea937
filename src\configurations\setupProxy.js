const { createProxyMiddleware } = require("http-proxy-middleware");
let devTarget = `${process.env.REACT_APP_SERVER_URL}`;

module.exports = function (app) {
  app.use(
    createProxyMiddleware("/web", {
      target: devTarget,
      changeOrigin: true,
      secure: false,
      logLevel: "debug",
    })
  );

  app.use(
    createProxyMiddleware("/nfs", {
      // target:  10.8.24.168:9000
      target: "https://d38i6zx90cpz04.cloudfront.net",
      changeOrigin: true,
      secure: false,
      logLevel: "debug",
    })
  );
  app.use(
    createProxyMiddleware("/zata", {
      target: devTarget,
      changeOrigin: true,
      secure: false,
      logLevel: "debug",
    })
  );
  app.use(
    createProxyMiddleware("/api", {
      target: devTarget,
      changeOrigin: true,
      secure: false,
      logLevel: "debug",
    })
  );
};

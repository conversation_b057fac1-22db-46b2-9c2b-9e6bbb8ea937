import { getStoreOutlet } from "@/services/DashboardService";
import { getClinetsList } from "@/services/CompanyService";
import { addProductList } from "@/services/ProductService";
import { REACT_PRODUCT_LIST } from "@/router/ReactEndPoints";
import { useNavigate } from "react-router-dom";
import { useSnackbar } from "notistack";
import CommonUtil from "@/util/CommonUtils";
export const getStores = (client, setOutlets) => {
  let UerInfo = JSON.parse(localStorage.getItem("USER"));
  let params = {
    clientId: client.id,
    userId: UerInfo.userId,
  };
  getStoreOutlet(params).then((res) => {
    if (res?.data?.code === "LVLI0000") {
      setOutlets(res.data.data);
    } else {
      setOutlets([]);
    }
  });
};

export const handleChange = (event, setPayload, setError, payload, error) => {
  const name = event.target.name;
  setPayload({
    ...payload,
    [name]: event.target.value,
  });

  setError({
    ...error,
    [name]: "",
  });
};

export const validateForm = (payload, setError, error, t) => {
  if (CommonUtil.isEmptyString(payload?.productName)) {
    setError({
      ...error,
      productName: `${t("LVL0001")}`,
    });
    return;
  }

  if (CommonUtil.isEmpty(payload.barCode)) {
    setError({
      ...error,
      barCode: `${t("LVL0001")}`,
    });
    return;
  }

  if (CommonUtil.isEmpty(payload.categoryLevel1)) {
    setError({
      ...error,
      categoryLevel1: `${t("LVL0001")}`,
    });
    return;
  }

  return true;
};
export const saveSubmit = (
  setIsLoading,
  payload,
  setError,
  isValid,
  client,
  outlet,
  t
) => {
  setIsLoading(true);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  if (isValid) {
    let params = {
      ...payload,
      clientId: client?.id,
      outletId: outlet?.id,
    };
    addProductList(params)
      .then((res) => {
        if (res.status === 200) {
          enqueueSnackbar(t("PLACE06"), {
            variant: "success",
          });
          navigate(REACT_PRODUCT_LIST);
          setError();
          setIsLoading(false);
        }
      })
      .catch((err) => {
        console.log(err);
        setIsLoading(false);
      });
  }
};

export const backClick = (navigate) => {
  navigate(REACT_PRODUCT_LIST);
};

export const getClinets = (setClients) => {
  const params = {
    pageNumber: 0,
    pageSize: 0,
    type: "RETAIL_CLIENT",
  };
  getClinetsList(params).then((res) => {
    if (res?.data?.code === "LVLI0000" && res?.data?.data) {
      setClients(res?.data?.data);
    }
  });
};

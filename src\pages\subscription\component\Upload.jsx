import React, { useState, useRef, useEffect } from "react";
import { Grid, FormHelperText } from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import { useTranslation } from "react-i18next";
import { ReactComponent as InActivePersontMenu } from "@/assets/images/UploadLogo.svg";
import FileUpload from "@/components/Retail-components/FileUpload";
import { handleComtractUpload } from "../js/index";
function Upload(props) {
  const { addFormik } = props;
  const { t } = useTranslation();
  return (
    <Grid item xs={6}>
      <Grid
        sx={{
          marginTop: "30px",
          display: "flex",
          justifyContent: "flex-start",
        }}
      >
        {addFormik.values.merchantUrl ? (
          <Grid
            sx={{
              height: "100px",
              position: "relative",
            }}
          >
            <ClearIcon
              sx={{
                position: "absolute",
                right: "0px",
                color: "#808080ba",
              }}
              onClick={() => {
                addFormik.setFieldValue("merchantUrl", "");
              }}
            ></ClearIcon>
            <img
              style={{
                height: "100px",
              }}
              src={addFormik.values.merchantUrl}
            ></img>
          </Grid>
        ) : (
          <FileUpload
            accept={{
              "image/*": [".jpeg", ".png"],
            }}
            onUpload={(data) => {
              handleComtractUpload(data, 0);
            }}
          >
            <Grid
              sx={{
                height: "40px",
                display: "flex",
                alignItems: "center",
                whiteSpace: "nowrap",
              }}
            >
              {t("subscription.uploadCompanyLogo")}
              <Grid ml={2}>
                <InActivePersontMenu></InActivePersontMenu>
              </Grid>
            </Grid>
          </FileUpload>
        )}

        {addFormik.touched.merchantUrl && addFormik.errors.merchantUrl && (
          <FormHelperText error id="name-error">
            {addFormik.errors.merchantUrl}
          </FormHelperText>
        )}
      </Grid>
    </Grid>
  );
}

export default Upload;

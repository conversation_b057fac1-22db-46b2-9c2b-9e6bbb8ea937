import { useDispatch, useSelector } from "react-redux";
import { fetchResource } from "@/store/reducers/resource";

import i18n from "i18next";
import { store } from "@/store";

const containsString = (array, target) => {
  return array.some((element) => element.includes(target));
};

export const onRouteBefore = ({ pathname, meta }) => {
  const {  resourceIds,isLoad } = useSelector(
    (state) => state.resource
  );

  if (meta.id) {
    if (isLoad === "load") {
      if (!containsString(resourceIds, meta.id)) {
        return "/404";
      }
    }
  }
};

import { Navigate, useLocation, useNavigate } from "react-router-dom";
function getDataType(data) {
  return Object.prototype.toString.call(data).match(/\s(\w+)\]/)[1];
}

export default class Fn {
  routes;
  onRouteBefore;
  constructor(option) {
    this.routes = option.routes || [];
    this.onRouteBefore = option.onRouteBefore;
  }

  transformRoutes(routeList = this.routes) {
    const list = [];
    routeList.forEach((route) => {
      const obj = { ...route };
      if (obj.path === undefined) {
        return;
      }
      if (obj.redirect) {
        obj.element = <Navigate to={obj.redirect} replace={true} />;
        delete obj.redirect;
      } else if (obj.element) {
        let meta = {
          id: obj.id,
          ...(obj.meta || {}),
        };
        if (this.onRouteBefore) {
          const result = this.onRouteBefore({ meta });
          let type = getDataType(result);
          if (type === "String") {
            obj.element = <Navigate to={result} replace={true} />;
          }
        }
      }
      if (obj.children) {
        obj.children = this.transformRoutes(obj.children);
      }
      list.push(obj);
    });
    return list;
  }
}

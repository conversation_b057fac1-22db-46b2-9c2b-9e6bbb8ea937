{"name": "retail-ai-analysis-frontend", "version": "0.1.0", "private": true, "dependencies": {"@babel/core": "^7.16.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@material-ui/core": "^4.12.3", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.155", "@mui/material": "^5.11.8", "@mui/styles": "^5.5.0", "@mui/x-data-grid": "^6.9.1", "@mui/x-date-pickers": "^5.0.20", "@mui/x-tree-view": "^7.11.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@react-google-maps/api": "^2.18.1", "@reduxjs/toolkit": "^2.2.6", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "amfe-flexible": "2.2.1", "antd": "^4.24.8", "apexcharts": "^3.37.1", "axios": "^1.3.2", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "date-fns": "^2.29.3", "dayjs": "^1.11.10", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react-refresh": "^0.4.4", "eslint-webpack-plugin": "^3.1.1", "express": "^4.18.2", "file-loader": "^6.2.0", "formik": "^2.4.6", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "i": "^0.3.7", "i18next": "^22.4.9", "i18next-browser-languagedetector": "^7.0.1", "i18next-xhr-backend": "^3.2.2", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "jsencrypt": "^3.3.2", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "material-react-table": "^2.13.0", "material-ui-popup-state": "^5.0.9", "mini-css-extract-plugin": "^2.4.5", "notistack": "^2.0.8", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-app-polyfill": "^3.0.0", "react-confirm-alert": "^3.0.6", "react-copy-to-clipboard": "^5.1.0", "react-dev-utils": "^12.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-element-to-jsx-string": "^15.0.0", "react-file-base64": "^1.0.3", "react-geocode": "^0.2.3", "react-google-maps": "^9.4.5", "react-i18next": "^12.1.5", "react-image-lightbox": "^5.1.4", "react-lodash": "^0.1.2", "react-measure": "^2.5.2", "react-phone-input-2": "^2.15.1", "react-redux": "^8.0.5", "react-refresh": "^0.11.0", "react-router-dom": "^6.8.1", "react-spinners": "^0.13.8", "react-window": "^1.8.10", "resize-observer-polyfill": "^1.5.1", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass-loader": "^12.3.0", "semver": "^7.3.5", "sockjs-client": "^1.6.1", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "typeface-roboto": "^1.1.13", "web-vitals": "^2.1.4", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1", "yarn": "^1.22.22", "yup": "^1.4.0"}, "scripts": {"start": "set \"REACT_APP_SERVER_URL=https://retailai.zkdigimaxstaging.com\" && node scripts/start.js", "start:ZY": "set \"REACT_APP_SERVER_URL=http://**********:9000\" && node scripts/start.js", "start:ZH": "set \"REACT_APP_SERVER_URL=http://***********:9000\" && node scripts/start.js", "start:ZC": "set \"REACT_APP_SERVER_URL=http://***********:9000\" && node scripts/start.js", "start:dev": "set \"REACT_APP_SERVER_URL=https://zata.zkdigimax.com\" && node scripts/start.js", "start:test": "set \"REACT_APP_SERVER_URL=http://*************:9000\" && node scripts/start.js", "start:localserver": "set \"REACT_APP_SERVER_URL=http://127.0.0.1:9000\" && node scripts/start.js", "start:awstest": "set \"REACT_APP_SERVER_URL=https://retail-ai.zkdigimaxdev.com\" && node scripts/start.js", "start:cnDev": "set \"REACT_APP_SERVER_URL=https://zata.zktecoiotdev.com\" && node scripts/start.js", "build": "node scripts/build.js", "test": "node scripts/test.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "copy-to-clipboard": "^3.3.3", "postcss-pxtorem": "^6.1.0", "webpack-cli": "^5.1.4"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}
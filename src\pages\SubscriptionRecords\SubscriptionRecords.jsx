import React, { useEffect, useState } from "react";
import { Grid, Box, Button } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import ListLayout from "@/components/ListLayout";
import { useSnackbar } from "notistack";
import MainCard from "@/components/Retail-components/MainCard";
import SearchForm from "@/components/Retail-components/SearchForm";
import { REACT_SUB_HISTORY_ADD } from "@/router/ReactEndPoints";
import QueryButton from "@/components/TableButton/QueryButton";
import MaterialTable from "./MaterialTable";
import { loadRecordsList } from "@/services/subscription";
function SubscriptionRecords(props) {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(false);
  const [rowCount, setRowCount] = useState(0);
  const [records, setRecords] = useState([]);
  const [currentItem, setCurrentItem] = useState(null);
  const [queryParams, setQueryParams] = useState({});
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageNumber: 1,
    pageSize: 10,
  });

  // 构建参数
  const buildParams = () => {
    const params = {
      pageNumber: pagination.pageNumber,
      pageSize: pagination.pageSize,
      ...queryParams,
    };
    return params;
  };

  const loadData = () => {
    setIsLoading(true);
    try {
      loadRecordsList(buildParams()).then((response) => {
        if (response?.data?.code === "LVLI0000") {
          setRecords(response?.data?.data?.objects);
          setRowCount(response?.data?.data?.totalCount);
          setIsLoading(false);
        } else if (response?.data?.code === "LVLE0054") {
          setRecords([]);
          setRowCount(0);
          setIsLoading(false);
        }
      });
    } catch (error) {
      enqueueSnackbar("Fail loading..." + error, {
        variant: "error",
      });
      setIsLoading(false);
    }
  };

  const formConfig = [
    {
      name: "merchantName",
      placeholder: t("subscription.customerName"),
      type: "input",
    },
    {
      name: "contactEmail",
      placeholder: t("subscription.customerAccount"),
      type: "input",
    },
    // {
    //   name: "createDate",
    //   placeholder: t("Subscripton Creation Date"),
    //   type: "input",
    // },
    // {
    //   name: "expirationDate",
    //   placeholder: t("Expiration Date"),
    //   type: "input",
    // },
  ];

  const searchLoadData = (data) => {
    setPagination({
      ...pagination,
      pageIndex: 1,
    });
    setQueryParams({
      ...data,
    });
  };

  useEffect(() => {
    loadData();
  }, [pagination.pageNumber, pagination.pageSize, queryParams]);

  return (
    <ListLayout navigateBack={false} title={t("PCS132")}>
      <MainCard border={false} height={"120px"}>
        <Grid mt={3}>
          <SearchForm
            formConfig={formConfig}
            searchFn={searchLoadData}
            loadData={loadData}
            setQueryParams={setQueryParams}
          ></SearchForm>
        </Grid>
      </MainCard>

      <Grid mt={4}>
        <MaterialTable
          pagination={pagination}
          setPagination={setPagination}
          rowCount={rowCount}
          setCurrentItem={setCurrentItem}
          isLoading={isLoading}
          records={records}
        ></MaterialTable>
      </Grid>
    </ListLayout>
  );
}
export default SubscriptionRecords;

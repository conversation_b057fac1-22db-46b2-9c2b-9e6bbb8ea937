import React, { useState } from "react";
import OnboardLayout from "@/layout/components/OnboardLayout";
import { Box, Grid, Typography } from "@mui/material";
import { But<PERSON> } from "antd";
import CustomInput from "../../../components/CustomInput";
import { useNavigate } from "react-router-dom";
import CommonUtil from "../../../util/CommonUtils";
import { ReactComponent as VisibilityIcon } from "@/assets/images/icon_viewoff.svg";
import { ReactComponent as VisibilityOffIcon } from "@/assets/images/icon_viewon.svg";
import { useSnackbar } from "notistack";
import { activatePersonAccount } from "../../../services/PersonService";
import { t } from "i18next";
export default function ActivatePersonAccount() {
  const [visibility, setVisibility] = useState(true);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  var urlValue = window.location.href;
  var url = new URL(urlValue);
  var userIdentifierData = url.searchParams.get("e");
  var idData = url.searchParams.get("t");
  var companyCodeData = url.searchParams.get("c");
  var roleIdData = url.searchParams.get("r");
  const [payload, setPayload] = useState({
    id: "",
    roleId: "",
    password: "",
    confirmPassword: "",
    userIdentifier: "",
    companyCode: "",
  });
  const [error, setError] = useState({
    reoleId: "",
    id: "",
    password: "",
    confirmPassword: "",
    userIdentifier: "",
    companyCode: "",
  });
  const handleSubmit = () => {
    if (validatePayload()) {
      var state = {
        ...payload,
        userIdentifier: userIdentifierData,
        id: idData,
        companyCode: companyCodeData,
        roleId: roleIdData,
      };
      activatePersonAccount(state).then((response) => {
        if (response?.data?.code === "LVLI0012") {
          enqueueSnackbar(response.data.message, {
            variant: "success",
            anchorOrigin: {
              horizontal: "center",
              vertical: "top",
            },
            style: {
              marginTop: "100px",
            },
          });
          return;
        } else enqueueSnackbar(response.data.message, { variant: "error" });
      });
      navigate("/");
    }
  };

  const validatePayload = () => {
    if (CommonUtil.isEmpty(payload.password)) {
      setError({
        ...error,
        password: "Password is required",
      });
      return false;
    }

    if (payload.password !== payload.confirmPassword) {
      setError({
        ...error,
        confirmPassword: t("LVLDAC0026"),
      });
      return false;
    }

    return true;
  };

  const handleChange = (event) => {
    const name = event.target.name;
    setPayload({
      ...payload,
      [name]: event.target.value,
    });
    setError({
      ...error,
      [name]: "",
    });
  };
  return (
    <OnboardLayout>
      <Typography variant="title">{t("LVLDAC0027")}</Typography>
      <Grid container spacing={2} px={2}>
        <Grid item xs={12}>
          <CustomInput
            required
            label={t("LVLDAC0028")}
            size="small"
            name="password"
            type="password"
            placeholder={t("LVLDAC0028")}
            error={error.password}
            helperText={error.password}
            handleChange={handleChange}
          />
        </Grid>
      </Grid>
      <Grid container spacing={2} px={2}>
        <Grid item xs={12}>
          <CustomInput
            required
            label={t("LVLDAC0030")}
            size="small"
            name="confirmPassword"
            placeholder={t("LVLDAC0030")}
            error={error.confirmPassword}
            helperText={error.confirmPassword}
            handleChange={handleChange}
            type={visibility ? "password" : "text"}
            autoComplete="off"
            InputProps={{
              endAdornment: visibility ? (
                <VisibilityOffIcon
                  style={{ cursor: "pointer" }}
                  onClick={() => {
                    setVisibility(!visibility);
                  }}
                />
              ) : (
                <VisibilityIcon
                  style={{ cursor: "pointer" }}
                  onClick={() => {
                    setVisibility(!visibility);
                  }}
                />
              ),
            }}
          />
        </Grid>
      </Grid>
      <Box
        sx={{
          flexWrap: "wrap",
          justifyContent: "flex-start",
          alignContent: "center",

          pt: 4,
          pb: 12,
        }}
      >
        <Button
          onClick={handleSubmit}
          style={{
            width: "100%",
            backgroundColor: "#6495ED",
            borderRadius: "4px",
            color: "white",
            height: "40px",
            fontSize: 15,
            fontStyle: "normal",
            border: "none",
            cursor: "pointer",
            overflow: "visible",
            "&:hover": {
              backgroundColor: "red",
            },
          }}
        >
          {t("LVLOT0016")}
        </Button>
      </Box>
    </OnboardLayout>
  );
}

<!DOCTYPE html>
<html lang="en" style="height: 100%">
  <head>
    <title>L3</title>
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
  </head>
  <style>
    input::-ms-reveal,
    input::-ms-clear {
      display: none;
    }
  </style>

  <body style="margin: 0px">
    <script>
      window.IS_REACT_SERVER_COMPONENT_BYPASS_WARNING_ENABLED = true;
    </script>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=5.0, minimum-scale=1"
    />
    <div id="root" style="height: 100%">
    <style>
      .loader {
        width: 62px;
        height: 62px;
        margin: 20% auto;
        position: relative;
      }

      .loader:before {
        content: "";
        width: 68px;
        height: 10px;
        background: #f0808050;
        position: absolute;
        top: 72px;
        left: 0;
        border-radius: 50%;
        animation: shadow324 0.5s linear infinite;
      }

      .loader:after {
        content: "";
        width: 100%;
        height: 100%;
        background: #f08080;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 6px;
        animation: jump7456 0.5s linear infinite;
      }
      span {
        font-size: 68px;
        margin: 6% auto;
      }

      @keyframes jump7456 {
        15% {
          border-bottom-right-radius: 3px;
        }

        25% {
          transform: translateY(9px) rotate(22.5deg);
        }

        50% {
          transform: translateY(18px) scale(1, 0.9) rotate(45deg);
          border-bottom-right-radius: 40px;
        }

        75% {
          transform: translateY(9px) rotate(67.5deg);
        }

        100% {
          transform: translateY(0) rotate(90deg);
        }
      }

      @keyframes shadow324 {
        0%,
        100% {
          transform: scale(1, 1);
        }

        50% {
          transform: scale(1.2, 1);
        }
      }
    </style>

    <div class="loader"></div>
  </body>
</html>

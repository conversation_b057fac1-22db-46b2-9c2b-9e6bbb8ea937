import React, { useEffect, useState } from "react";
// import { TreeItem, TreeView } from "@mui/lab";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { Checkbox } from "@mui/material";

import { SimpleTreeView } from "@mui/x-tree-view/SimpleTreeView";
import { TreeItem } from "@mui/x-tree-view/TreeItem";

/**
 * associateChild 是否关联子节点
 * @param {*} param0
 * @returns
 */
const CheckableTree = ({
  getItemId = (item) => item.id,
  getItemLabel = (item) => item.name,
  getChildren = (item) => item.children,
  associateChild = true,
  multiSelect = false,
  treeData = [],
  selectedKeys = [],
  onSelect = (nodeIds) => {},
  isExpandAll = false,
}) => {
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);

  useEffect(() => {
    
    if (isExpandAll) {
      let ids = getAllChildNodeIds(treeData);
      setExpandedKeys(ids);
    } else {
      setExpandedKeys(selectedKeys);
    }
  }, [treeData]);

  useEffect(() => {
    setCheckedKeys(selectedKeys);
  }, [selectedKeys]);

  // 递归获取所有子节点的 ID
  const getAllChildNodeIds = (nodes) => {
    let childNodeIds = [];
    nodes?.forEach((node) => {
      childNodeIds.push(getItemId(node));
      if (Array.isArray(getChildren(node))) {
        childNodeIds = [
          ...childNodeIds,
          ...getAllChildNodeIds(getChildren(node)),
        ];
      }
    });
    return childNodeIds;
  };

  const onCheck = (event, id, checked, childNodeIds) => {
    
    event.checkClick = true;
    event.stopPropagation();
    let newCheckedKeys = [...checkedKeys];
    let aNodeIds = associateChild ? [id, ...childNodeIds] : [id];
    if (checked) {
      newCheckedKeys = [...newCheckedKeys, ...aNodeIds];
    } else {
      newCheckedKeys = newCheckedKeys.filter((key) => !aNodeIds.includes(key));
    }
    setCheckedKeys(newCheckedKeys);
    onSelect(newCheckedKeys);
  };

  const renderTree = (nodes) => {
    return nodes.map((node) => {
      const label = (
        <div>
          <Checkbox
            onChange={(event, checked) => {
              event.stopPropagation();
              onCheck(
                event,
                getItemId(node),
                checked,
                getAllChildNodeIds(getChildren(node))
              );
              return false;
            }}
            checked={checkedKeys.includes(getItemId(node))}
          />
          {getItemLabel(node)}
        </div>
      );

      return (
        <TreeItem key={getItemId(node)} itemId={getItemId(node)} label={label}>
          {Array.isArray(getChildren(node))
            ? renderTree(getChildren(node))
            : null}
        </TreeItem>
      );
    });
  };

  const onExpand = (event, nodeIds) => {
    if (event.checkBox) {
      let changeId = event.checkNodeId;
      let index = nodeIds.indexOf(changeId);
      if (index !== -1) {
        nodeIds.splice(index, 1);
      } else {
        nodeIds.push(changeId);
      }
    } else {
      setExpandedKeys(nodeIds);
    }
  };

  const onItemExpansionToggle = (event, itemId, isExpanded) => {
    

    if (event.target.checked !== undefined) {
      event.checkBox = true;
      event.checkNodeId = itemId;
    }
  };

  return (
    <SimpleTreeView
      multiSelect={multiSelect}
      aria-label="file system navigator"
      sx={{
        ".Mui-focused": {
          backgroundColor: "#ffffff",
        },
      }}
      expandedItems={expandedKeys}
      onExpandedItemsChange={onExpand}
      onItemExpansionToggle={onItemExpansionToggle}
      selected={checkedKeys}
    >
      {renderTree(treeData)}
    </SimpleTreeView>
  );
};

export default CheckableTree;

import { Box, Grid, Paper, Select, Typography } from "@mui/material";
import React from "react";
import { ReactComponent as PeopleCountingBlueImg } from "@/assets/images/ai_people_counting_blue.svg";
import { ReactComponent as PeopleCountingGreenImg } from "@/assets/images/ai_people_counting_green.svg";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import CustomDatePicker from "./CustomDatePicker";
// function Item(props) {
//   const { sx, ...other } = props;
//   return (
//     <Box
//       sx={{
//         p: 1,
//         m: 1,
//         ...sx,
//       }}
//       {...other}
//     />
//   );
// }

export default function AIDashboardCard(props) {
  const { t } = useTranslation();
  let changeDate = (e) => {
    if (props.changeDate) {
      props.changeDate(e);
    }
  };

  return (
    <>
      <Box
        component={Paper}
        elevation={0}
        variant="elevation"
        display={"flex"}
        width={"100%"}
        bgcolor={props.variant === "blue" ? "#ebf5fb" : "#f0f8f3"}
      >
        <Box sx={{ p: 1, m: 1 }}>
          {props.variant === "blue" ? (
            <PeopleCountingBlueImg />
          ) : (
            <PeopleCountingGreenImg />
          )}
        </Box>
        <Box sx={{ p: 1, m: 1, width: "60%" }}>
          <Box display={"block"} justifyContent={"flex-start"}>
            <Box p="0" m="0">
              <Typography variant="count">{props.count}</Typography>
            </Box>
            <Box mt={1} p="0">
              <Typography variant="countDescription">
                {props.variant === "blue"
                  ? "Total Number of People Entering"
                  : "Total Number of People Passing by"}
              </Typography>
            </Box>
            <Box p="0" m="0">
              <Typography variant="countSubDescription">
                Base on ReID Algorithm
              </Typography>
            </Box>
          </Box>
        </Box>
        <Box
          display={"block"}
          alignItems={"flex-start"}
          style={{
            position: "relative",
          }}
          sx={{ p: 1, m: 1, width: "30%" }}
        >
          <Box
            sx={{ width: "140px", position: "absolute", left: "-40px" }}
            item
          >
            <CustomDatePicker
              size={"small"}
              disableFuture={true}
              date={props.date}
              disabled={false}
              fullWidth={true}
              label={""}
              placeholder={t("LVLRCP016")}
              SelectedDate={(e) => changeDate(e)}
            />
          </Box>
        </Box>
      </Box>
    </>
  );
}

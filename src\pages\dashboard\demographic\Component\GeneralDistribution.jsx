import { Box, Grid, Paper, Button, Typography } from "@mui/material";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { DatePicker, Space } from "antd";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import ReactApexChart from "react-apexcharts";
import FormatPreValue from "./FormatPreValue";
import {
  getValue,
  gainAllDateBetRange,
  setOption,
  preRange,
  preLastYear,
} from "./utils";
const { RangePicker } = DatePicker;
dayjs.extend(customParseFormat);

export default function GeneralDistribution() {
  const [compareType, setCompareType] = useState("none");
  const [rangeValue, setRangeValue] = useState([]);
  const [preRangeValue, setPreRangeValue] = useState([]);
  const [options, setOptions] = useState({});
  const [series, setSeries] = useState([]);
  const [dateArr, setDateArr] = useState([]);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (compareType === "none") {
      loadData();
    } else {
      loadCompareData();
    }
  }, [compareType, dateArr]);

  const renderData = () => {
    return dateArr.map(() => {
      return getValue();
    });
  };

  const loadData = () => {
    const series = [
      {
        type: "line", //render a line chart for this data
        name: "Male",
        data: renderData(),
      },
      {
        type: "line", //use column chart here.
        name: "Female",
        data: renderData(),
      },
    ];
    const options = {
      xaxis: {
        categories: dateArr,
      },
      colors: ["#768CD1", "#A7D690"],
      stroke: {
        width: 1,
      }
    };
    setOption(options, dateArr);
    setSeries(series);
    setOptions(options);
  };

  const loadCompareData = () => {
    const series = [
      {
        type: "line", //render a line chart for this data
        name: "Male",
        data: renderData(),
      },

      {
        type: "line", //render a line chart for this data
        name: "Male(Pre , Period)",
        data: renderData(),
      },

      {
        type: "line", //use column chart here.
        name: "Female",
        data: renderData(),
      },

      {
        type: "line", //use column chart here.
        name: "Female(Pre , Period)",
        data: renderData(),
      },
    ];
    const options = {
      xaxis: {
        categories: dateArr,
      },
      colors: ["#768CD1", "#5E6A90", "#A7D690", "#84A35E"],
      stroke: {
        width: 1,
      }
    };
    setOption(options, dateArr);
    setSeries(series);
    setOptions(options);
  };

  const handleChange = (e) => {
    let value = e.target.value;
    setCompareType(value);
    if (value === "none") {
      setPreRangeValue([]);
    } else if (value === "previous") {
      setPreRangeValue(preRange(rangeValue));
    } else if (value === "LastYear") {
      setPreRangeValue(preLastYear(rangeValue));
    }
  };

  const RangePickerChange = (e) => {
    setRangeValue(e);
    if (e && e.length === 2) {
      if (compareType === "none") {
        setPreRangeValue([]);
      } else if (compareType === "previous") {
        setPreRangeValue(preRange(e));
      } else if (compareType === "LastYear") {
        setPreRangeValue(preLastYear(e));
      }
      let attr = gainAllDateBetRange(e);
      setDateArr(attr);
    } else {
      setDateArr([]);
    }
  };
  let boxStyle = {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
  };

  return (
    <Box style={{ height: "100%" }} p={2} component={Paper}>
      <Box m={2}>
        <Typography
          style={{
            fontWeight: 400,
          }}
          variant="title"
        >
          General Distribution
        </Typography>
      </Box>

      <Grid
        m={1}
        sx={{
          display: "flex",
          flexWrap: "wrap",
        }}
      >
        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            m: 1,
          }}
        >
          <Typography sx={{ mr: 2 }}>Date Range</Typography>
          <RangePicker
            value={rangeValue}
            onChange={(e) => {
              RangePickerChange(e);
            }}
            format="DD/MM/YYYY"
          />
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            m: 1,
          }}
        >
          <Typography sx={{ mr: 2 }}>Compare with</Typography>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={compareType}
            label=""
            sx={{
              p: 0,
            }}
            onChange={handleChange}
          >
            <MenuItem value={"none"}>None</MenuItem>
            <MenuItem value={"previous"}>Previous Period</MenuItem>
            <MenuItem value={"LastYear"}>Last Year</MenuItem>
          </Select>
        </Grid>

        {compareType !== "none" && (
          <Grid m={1}>
            <RangePicker
              value={preRangeValue}
              disabled={true}
              format="DD/MM/YYYY"
            />
          </Grid>
        )}
      </Grid>

      <Box>
        <ReactApexChart
          options={options}
          series={series}
          type="line"
          height={350}
        />
      </Box>

      {compareType !== "none" && (
        <Grid
          sx={{
            justifyContent: "space-around",
          }}
          container
        >
          <Grid style={boxStyle} p={2} item>
            <FormatPreValue
              style={{ fontSize: "20px" }}
              value={getValue(true)}
              isIcon={true}
            ></FormatPreValue>
            <Typography sx={{ mt: 2 }}>Male Change</Typography>
          </Grid>
          <Grid style={boxStyle} p={2} item>
            <FormatPreValue
              style={{ fontSize: "20px" }}
              value={getValue(true)}
              isIcon={true}
            ></FormatPreValue>
            <Typography sx={{ mt: 2 }}>Female Change</Typography>
          </Grid>
          <Grid style={boxStyle} p={2} item>
            <FormatPreValue
              style={{ fontSize: "20px" }}
              value={getValue(true)}
              isIcon={true}
            ></FormatPreValue>
            <Typography sx={{ mt: 2 }}>Overall Change</Typography>
          </Grid>
        </Grid>
      )}
    </Box>
  );
}

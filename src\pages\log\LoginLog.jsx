import React, { useState } from "react";
import ListLayout from "@/components/ListLayout";
import { useTranslation } from "react-i18next";
import { Tooltip, Grid } from "@mui/material";
import { ReactComponent as RefreshIcon } from "@/assets/images/icon_refresh.svg";
import RingLoader from "react-spinners/RingLoader";
import DataTable from "@/components/DataTable";
import CommonUtil from "@/util/CommonUtils";
import { getLoginLog } from "@/services/log";
import LoginSearch from "./LoginSearch";
function LoginLog() {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [filters, setFilters] = useState({
    pageNumber: 1,
    pageSize: 5,
  });

  const defaultFilters = {
    pageNumber: 1,
    pageSize: 5,
  };
  const columns = [
    {
      field: "userName",
      headerName: `${t("login_log.user_name")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.userName} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.userName || "-")}</span>
        </Tooltip>
      ),
    },
    {
      field: "realName",
      headerName: `${t("login_log.account")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.realName} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.realName || "-")}</span>
        </Tooltip>
      ),
    },
    {
      field: "ipaddr",
      headerName: `${t("login_log.ipaddr")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.ipaddr} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.ipaddr)}</span>
        </Tooltip>
      ),
    },
    // {
    //   field: "location",
    //   headerName: `${t("login_log.address")}`,
    //   flex: 1,
    //   headerAlign: "center",
    //   align: "center",
    //   renderCell: (e) => (
    //     <Tooltip title={e.row.location} arrow placement="bottom">
    //       <span>{CommonUtil.formatLongText(e.row.location || "-")}</span>
    //     </Tooltip>
    //   ),
    // },
    {
      field: "accessTime",
      headerName: `${t("login_log.access_time")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.accessTime} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.accessTime || "-")}</span>
        </Tooltip>
      ),
    },

    {
      field: "msg",
      headerName: `${t("login_log.login_msg")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.msg} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.msg || "-")}</span>
        </Tooltip>
      ),
    },

    {
      field: "status",
      headerName: `${t("LVLDAC0013")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip
          title={
            e.row.status == "0"
              ? t("common.common_success")
              : t("common.common_fail")
          }
          arrow
          placement="bottom">
          <span>
            {CommonUtil.formatLongText(
              e.row.status == "0"
                ? t("common.common_success")
                : t("common.common_fail") || "-"
            )}
          </span>
        </Tooltip>
      ),
    },
  ];

  // 生成假数据
  const loadData = () => {
    let payload = {
      ...filters,
    };
    setIsLoading(true);
    getLoginLog(payload).then((res) => {
      setRecords(res?.data?.data?.objects);
      setTotalRecords(res?.data?.data?.totalCount);
    });

    setIsLoading(false);
  };

  // 在组件加载时生成假数据
  React.useEffect(() => {
    loadData();
  }, [filters]);

  const handlePageChange = (e) => {
    setFilters({
      ...filters,
      pageNumber: e + 1,
    });
  };

  const handlePageSize = (e) => {
    setFilters({
      ...defaultFilters,
      pageNumber: defaultFilters.pageNumber,
      pageSize: e,
    });
  };

  const handleSearch = (searchParams) => {
    setFilters({
      ...filters,
      ...searchParams,
      pageNumber: 1,
    });
  };

  const handleReset = () => {
    setFilters(defaultFilters);
  };

  return (
    <React.Fragment>
      <ListLayout title={t("login_log.title")}>
        <LoginSearch onSearch={handleSearch} onReset={handleReset} />

        <Grid
          container
          xs={12}
          sx={{
            display: "flex",
            justifyContent: "flex-end",
          }}>
          <Grid item mt={4}>
            <RefreshIcon width={"35"} height={"35"} className="pointer" />
          </Grid>

          {isLoading ? (
            <RingLoader
              color={"#597ef7"}
              loading={isLoading}
              cssOverride={{
                display: "block",
                margin: "10% auto",
                borderColor: "#b37feb",
              }}
              size={60}
              speedMultiplier={3}
              aria-label="Loading Spinner"
              data-testid="loader"
            />
          ) : (
            <DataTable
              columns={columns}
              rows={records}
              page={filters.pageNumber - 1}
              totalRecords={totalRecords}
              rowsPerPage={filters.pageSize}
              onPageChange={(pn) => handlePageChange(pn)}
              onPageSizeChange={(ps) => handlePageSize(ps)}
              checkboxSelection={false}
            />
          )}
        </Grid>
      </ListLayout>
    </React.Fragment>
  );
}

export default LoginLog;

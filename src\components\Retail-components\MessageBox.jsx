import React from "react";
import Alert from "@mui/material/Alert";
import { Box, Toolbar, useMediaQuery } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef, useState } from "react";
import { hideMessage } from '@/store/reducers/message';
const MessageBox = () => {
    const { showMessage, message, type, variant } = useSelector(
        (state) => state.message
    );

    const timeOutObj = useRef(null);
    const dispatch = useDispatch();

    useEffect(() => {
        if (showMessage) {

            if (timeOutObj) {
                clearTimeout(timeOutObj.current);
            }
            timeOutObj.current = setTimeout(() => {
                dispatch(hideMessage())
            }, 4000);
        }
    }, [showMessage, type, message]);

    return (
        <Box
            sx={{
                position: "fixed",
                top: "20px",
                left: "45%",
                marginLeft: "10px",
                zIndex: 10000,
                borderRadius: '5px'
            }}
        >
            {showMessage && (
                <Alert sx={{
                    minWidth: '200px',
                    backgroundColor: '#ffffff'
                }} severity={type} variant={variant}>
                    {message}
                </Alert>
            )}
        </Box>
    );
};

export default MessageBox;

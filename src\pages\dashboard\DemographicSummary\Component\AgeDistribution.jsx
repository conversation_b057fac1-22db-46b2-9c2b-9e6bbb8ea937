import { Box, Grid } from "@mui/material";
import LineCharts from "./AgeLineCharts";
import { useTranslation } from "react-i18next";
function AgeDistribution() {
  const { t } = useTranslation();
  const blockStyle = {
    background: "#f2f2f2",
    borderRadius: "20px",
    textAlign: "center",
    height: "35vh",
    margin: "0vh 1vw 0vh 0vw",
  };

  const textStyle = {
    color: "#808080",
    margin: "0vh 2.5vw ",
  };
  return (
    <>
      <Grid
        m={1}
        sx={{
          background: "#FFF",
          borderRadius: "20px",
        }}
      >
        <Box fontWeight={700} fontSize={24} m={2}>
          {t("PCS56")}
        </Box>
        <LineCharts></LineCharts>
        {/* <Grid display={"flex"}>
          <LineCharts></LineCharts>

          <Grid sx={blockStyle}>
            <Box marginTop={3}>
              <Box sx={textStyle} fontSize={20}>
                CHART
              </Box>
              <Box sx={textStyle} fontSize={20}>
                LEGEND
              </Box>
            </Box>
          </Grid>
        </Grid> */}
      </Grid>
    </>
  );
}

export default AgeDistribution;

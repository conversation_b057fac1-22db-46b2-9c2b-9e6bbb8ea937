import React, { useEffect, useState, useContext } from "react";
import Dialog from "@mui/material/Dialog";
import {
  Button,
  Grid,
  Box,
  DialogActions,
  InputLabel,
  DialogContent,
  DialogTitle,
  MenuItem,
  Select,
  FormControl,
} from "@mui/material";
import { useTranslation } from "react-i18next";

import { VisitorDemographicContext } from "../DemographicSummary.jsx";
function AllStore({ storeOpen, setStoreOpen, setStoreName, storeList }) {
  const { t } = useTranslation();
  const { storeName, hideButton } = useContext(VisitorDemographicContext);
  // const [hideButton, setHideButton] = useState(false); //判断是都为超级管理员
  // useEffect(() => {
  //   checkUserRole(setHideButton);
  // }, []);
  return (
    <React.Fragment>
      <Dialog open={storeOpen} onClose={() => setStoreOpen(false)}>
        <DialogTitle fontSize={24} sx={{ minWidth: 300 }}>
          {t("PCS28")}
        </DialogTitle>
        <DialogContent>
          <Grid
            item
            xs={10}
            style={{ display: "flex", justifyContent: "flex-end" }}
          >
            <Box sx={{ minWidth: 200 }}>
              <FormControl fullWidth={true}>
                <InputLabel sx={{ marginTop: "-10px" }}>
                  {t("PCS29")}
                </InputLabel>
                <Select
                  id="demo-simple-select"
                  value={storeName}
                  label="Store Name"
                  onChange={(e) => {
                    const selectedValue = e.target.value;
                    if (selectedValue === 1) {
                      setStoreName("All");
                    } else {
                      setStoreName(selectedValue);
                    }
                    setStoreOpen(false);
                  }}
                >
                  {hideButton ? (
                    <MenuItem value={1}>{t("PCS30")}</MenuItem>
                  ) : null}
                  {storeList?.map((item) => (
                    <MenuItem key={item?.id} value={item}>
                      {item?.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>
        </DialogContent>
      </Dialog>
    </React.Fragment>
  );
}
export default AllStore;

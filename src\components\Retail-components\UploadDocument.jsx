/* eslint-disable react/display-name */
import React, { forwardRef, useRef, useState } from 'react'
import { Grid, Avatar } from '@mui/material'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import axios from 'axios'
import { useSnackbar } from 'notistack'
import { getToken } from '../utils/auth'
const UploadDocument = forwardRef((props, ref) => {

  const { accept=".jpg,.png" } = props

  const { enqueueSnackbar } = useSnackbar()
  const {
    value,
    formik,
    disabled,
    name,
    text,
    width = 100,
    height = 100,
  } = props
  const fileInputRef = useRef()
  const imageRef = useRef(null)
  React.useImperativeHandle(ref, () => ({
    handleFileChange,
    clickUploadImage,
  }))

  const handleFileChange = (event) => {
    const file = event.target.files[0]
    const reader = new FileReader()
    reader.onload = (e) => {
      const imageDataURL = e.target.result
      if (imageRef.current) {
        imageRef.current.src = imageDataURL
      }
    }
    if (file) {
      reader.readAsDataURL(file)
      var bodyFormData = new FormData()
      bodyFormData.append('key', text)
      bodyFormData.append('file', file)
      axios({
        method: 'post',
        url: '/dev/common/upload/file',
        data: bodyFormData,
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: getToken(),
        },
      }).then((res) => {
        if (res.data.code === '000000') {
          enqueueSnackbar('上传成功', {
            variant: 'success',
          })

          formik.setFieldValue(name, res.data.data)


        }
      })
    }
  }



  const clickUploadImage = () => {
    if (!disabled) {
      fileInputRef.current.click()
    }
  }

  return (
    <Grid
      sx={{
        display: 'flex',
        justifyContent: 'center',
        cursor: 'pointer',
        width: width + 'px',
        height: height + 'px',
        border: '1px solid #d8d8d8',
        background: '#f5f8fa',
        borderRadius: '10px',
        marginLeft: '10px',
      }}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        style={{ display: 'none' }}
        onChange={handleFileChange}
        disabled={props.isUploadDisabled}
      />
      <div
        onClick={clickUploadImage}
        style={{
          width: props.width,
          height: props.height,
          background: 'C3C5C7',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        }}
      >
        {formik.values[name] ? (
          <Grid
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              width: '100%',
              height: '100%',
            }}
          >

            {formik.values[name].endsWith('.zip') || formik.values[name].endsWith('.rar') ? <Grid>
              <a href={'http://www.tyds.com:5443/' + formik.values[name]} >下载证书</a>
            </Grid> : <img
              ref={imageRef}
              src={"http://www.tyds.com:5443/" + formik.values[name]}
              style={{
                width: '100%',
                height: '100%',
                borderRadius: '10px',
                zIndex: 5555,
                cursor: 'pointer',
              }}
              alt="Upload Icon"
            />}
          </Grid>
        ) : (
          <Grid
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <CloudUploadIcon></CloudUploadIcon>
            <Grid>文件上传</Grid>
          </Grid>
        )}
      </div>
    </Grid>
  )
})

export default UploadDocument

// {
//   imageRef.current !== null ? (
//     <img
//       ref={imageRef}
//       src={UploadIcon}
//       style={{ width: "100%", height: "100%", borderRadius: "10px" }}
//     />
//   ) : (
//     <Grid display={"flex"}>
//       <Avatar
//         className="ImageContent"
//         alt=""
//         ref={imageRef}
//         imgProps={{ draggable: "false" }}
//         src={UploadIcon}
//         style={{
//           height: "100%",
//           width: "100%",
//         }}
//       />

//       <div
//         style={{
//           whiteSpace: "nowrap",
//           marginLeft: "-20px",
//           marginTop: "30px",
//         }}
//       >
//         文件上传
//       </div>
//     </Grid>
//   );
// }

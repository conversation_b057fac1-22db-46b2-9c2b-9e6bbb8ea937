import React, { useState, useEffect } from "react";
import { Grid, Box } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { ReactComponent as AddIcon } from "@/assets/images/icon_add.svg";
import { ReactComponent as RefreshIcon } from "@/assets/images/icon_refresh.svg";
import AccessControl from "@/components/AccessControl";
function CustomAdd(props) {
  const navigate = useNavigate();
  const { authorization, route, loadData } = props;

  return (
    <Grid display={"flex"} justifyContent={"flex-end"} marginTop={2}>
      <AccessControl authorization={authorization}>
        <Box pr={2} onClick={() => navigate(route)}>
          <AddIcon width={"35"} height={"35"} className="pointer" />
        </Box>
      </AccessControl>
      <Box pr={2}>
        <RefreshIcon
          width={"35"}
          height={"35"}
          className="pointer"
          onClick={loadData}
        />
      </Box>
    </Grid>
  );
}

export default CustomAdd;

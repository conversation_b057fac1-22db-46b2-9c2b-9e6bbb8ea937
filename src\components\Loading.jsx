// material-ui
import { styled } from "@mui/material/styles";
import CircularProgress from "@mui/material/CircularProgress";
import PropTypes from "prop-types";
import { Box } from "@mui/material";

const Styles = {
  width: "100%",
  height: "100%",
  backgroundColor: "red",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
};

const Loading = (props) => {
  const { loading = false } = props;

  return (
    <>
      {loading && (
        <Box
          sx={{
            width: "100%",
            height: "85vh",
            backgroundColor: "#fffff",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <CircularProgress size={60} />
        </Box>
      )}
    </>
  );
};

Loading.propTypes = {
  loading: PropTypes.bool,
};

export default Loading;

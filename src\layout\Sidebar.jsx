import * as React from "react";
import { Box, Paper } from "@mui/material";
// import { ReactComponent as Logo } from "@/assets/images/l3-logo-small.svg";
import { ReactComponent as Logo } from "@/assets/logo/New Zata Logo Name.png";
import { useTranslation } from "react-i18next";
import SidebarMenuItem from "../layout/components/SidebarMenuItem";
import { ReactComponent as InActiveRetailClientMenu } from "@/assets/images/menu_retail_client.svg";
import { ReactComponent as ActiveRetailClientMenu } from "@/assets/images/menu_retail_client_active.svg";
import { ReactComponent as InActiveOutletMenu } from "@/assets/images/menu_outlet.svg";
import { ReactComponent as ActiveOutletMenu } from "@/assets/images/menu_outlet_active.svg";
import { ReactComponent as InActiveDeviceMenu } from "@/assets/images/menu_device.svg";
import { ReactComponent as ActiveDeviceMenu } from "@/assets/images/menu_device.svg";
import { ReactComponent as ActiveDashboardMenu } from "@/assets/images/menu_dashboard_in.svg";
import { ReactComponent as InActiveDashboardMenu } from "@/assets/images/menu_dashboard_out.svg";
import { ReactComponent as ActiveWorkshopMenu } from "@/assets/images/menu_workshop_in.svg";
import { ReactComponent as InActiveWorkshopMenu } from "@/assets/images/menu_workshop_out.svg";
import { ReactComponent as InActiveDataAccessMenu } from "@/assets/images/menu_data_access_control.svg";
import { ReactComponent as ActiveDataAccessMenu } from "@/assets/images/menu_data_access_control_active.svg";
import { ReactComponent as ActiveLocationMenu } from "@/assets/images/location_icon_in.svg";
import { ReactComponent as InActiveLocationMenu } from "@/assets/images/location_icon_out.svg";
import { ReactComponent as ActivePrincipalMenu } from "@/assets/images/menu_principal.svg";
import { ReactComponent as InActivePrincipalMenu } from "@/assets/images/menu_principal.svg";
import { ReactComponent as LanguageMenu } from "@/assets/images/menu_language.svg";
import { ReactComponent as authoritation } from "@/assets/images/authoritation.svg";
import RefreshToken from "../configurations/RefreshToken";
import UserProfileMenu from "./UserProfileMenu";
import SwitchLanguage from "./leftMenu/SwitchLanguage";
// import CompanyProfileMenu from "../components/layout-components/CompanyProfileMenu";
import CompanyProfileMenu from "./leftMenu/CompanyProfileMenu";

import CommonUtil from "../util/CommonUtils";

export default function Sidebar(props) {
  const { t } = useTranslation();
  const path = window.location.pathname;
  const loginType = CommonUtil.getLoginType();
  return (
    loginType && (
      <Box
        component={Paper}
        elevation={0}
        variant="outlined"
        my={2}
        ml={2}
        width={250}
        height={"95%"}
        justifyContent={"flex-start"}
        bgcolor={"black"}
        borderRadius={2}
        boxShadow="5px 5px 2px 1px #0000001f"
        flexDirection={"column"}>
        <Box display={"block"} height={"70%"}>
          <Box p={3}>
            <Logo />
          </Box>
          <Box
            height={"80%"}
            overflow={"auto"}
            sx={{
              "&::-webkit-scrollbar": {
                width: "6px",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "#888",
                borderRadius: "4px",
              },
            }}>
            <SidebarMenuItem
              link="/dashboard"
              label={t("LVLDB0001")}
              isActive={path.includes("/dashboard")}
              activeMenu={ActiveDashboardMenu}
              inActiveMenu={InActiveDashboardMenu}
            />
            {loginType === "HQ" && (
              <SidebarMenuItem
                link="/location/country"
                label={t("LVLDB0002")}
                isActive={path.includes("/location")}
                activeMenu={ActiveLocationMenu}
                inActiveMenu={InActiveLocationMenu}
              />
            )}

            {loginType === "HQ" && (
              <SidebarMenuItem
                link="/Authoritation"
                label={t("LVLDB0036")}
                isActive={path.includes("/Authoritation")}
                activeMenu={ActiveLocationMenu}
                inActiveMenu={InActiveLocationMenu}
              />
            )}
            {(loginType === "HQ" || loginType === "RETAILCLIENT") && (
              <SidebarMenuItem
                link="/retail-client"
                label={t("LVLDB0003")}
                isActive={path.includes("/retail")}
                activeMenu={ActiveRetailClientMenu}
                inActiveMenu={InActiveRetailClientMenu}
              />
            )}
            {loginType === "HQ" && (
              <>
                <SidebarMenuItem
                  link="/outlet"
                  label={t("LVLDB0004")}
                  isActive={
                    path.includes("/outlet") && !path.includes("/outlet_type")
                  }
                  activeMenu={ActiveOutletMenu}
                  inActiveMenu={InActiveOutletMenu}
                />
                <SidebarMenuItem
                  link="/device"
                  label={t("LVLDB0005")}
                  isActive={path.includes("/device")}
                  activeMenu={ActiveDeviceMenu}
                  inActiveMenu={InActiveDeviceMenu}
                />
              </>
            )}
            {(loginType === "HQ" || loginType === "PRINCIPAL") && (
              <SidebarMenuItem
                link="/principal"
                label={t("LVLDB0013")}
                isActive={path.includes("/principal")}
                activeMenu={ActivePrincipalMenu}
                inActiveMenu={InActivePrincipalMenu}
              />
            )}
            {loginType === "HQ" && (
              <SidebarMenuItem
                link="/data-access-control/Device"
                label={t("LVLDB0006")}
                isActive={path.includes("/data-access-control")}
                activeMenu={ActiveDataAccessMenu}
                inActiveMenu={InActiveDataAccessMenu}
              />
            )}
            <SidebarMenuItem
              link="/workshop"
              label={t("LVLDB0007")}
              isActive={path.includes("/workshop")}
              activeMenu={ActiveWorkshopMenu}
              inActiveMenu={InActiveWorkshopMenu}
            />
          </Box>
        </Box>
        <Box display={"grid"} sx={{ alignItems: "flex-end", height: "30%" }}>
          <Box item>
            <CompanyProfileMenu />
            <UserProfileMenu />

            {/* <SidebarMenuItem
            link=""
            label="Language"
            isActive={false}
            activeMenu={LanguageMenu}
            inActiveMenu={LanguageMenu}
          />
          */}
            <SwitchLanguage />
          </Box>
        </Box>
        {/* <RefreshToken /> */}
      </Box>
    )
  );
}

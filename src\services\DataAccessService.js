import api from "../configurations/http-common";

export const getAllApplications = async () => {
  return api.securedAxios().get("web/applications");
};

export const createApplicationDevice = async (payload) => {
  return api.securedAxios().post("web/application_device", payload);
};

export const getAllApplicationDevices = async () => {
  return api.securedAxios().get("web/application_devices");
};

export const deleteApplicationDevice = async (id) => {
  return api.securedAxios().delete("web/application_device" + "?" + "id=" + id);
};

export const createApplicationClient = async (payload) => {
  return api.securedAxios().post("web/application_client", payload);
};

export const getAllApplicationClients = async () => {
  return api.securedAxios().get("web/application_clients");
};

export const getClientApplicationsByApplicationId = async (id) => {
  return api.securedAxios().get("web/application_clients/" + id);
};

export const deleteApplicationClient = async (id) => {
  return api.securedAxios().delete("web/application_client" + "?" + "id=" + id);
};

export const bindPrincipalToApplication = async (payload) => {
  return api.securedAxios().post("web/application_principal", payload);
}

export const getPrincipalApplicationByApplicationId = async(id) => {
  return api.securedAxios().get("web/application_principals/" + id);
}

export const deleteApplicationPrincipal = async(id) => {
  return api.securedAxios().delete("web/application_principal/" + id);
} 

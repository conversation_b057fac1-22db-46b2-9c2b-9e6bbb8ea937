import { Box, Grid, Typography, useTheme } from "@mui/material";
import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { REACT_DATA_ACCESS_CONTROL } from "@/router/ReactEndPoints";

export default function DataAccessMenu({ onSelect }) {
  const { t } = useTranslation();
  let navigate = useNavigate();
  const theme = useTheme();
  const location = useLocation();
  const { name } = useParams();
  const [selected, setSelected] = React.useState(name);
  useEffect(() => {
    onSelect(selected);
  }, [selected, onSelect]);

  useEffect(() => {
    setSelected(name);
  }, [name]);

  const data = [
    { name: `${t("LVLDAC0002")}`, index: 0, code: "Device" },
    { name: `${t("LVLDAC0003")}`, index: 1, code: "RetailClient" },
    { name: `${t("LVLDAC0004")}`, index: 2, code: "Principals" },
  ];

  return (
    <Grid
      style={{
        padding: "14px 12px",
        marginLeft: "-12px",
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
      }}
      // onClick={() => console.info('params')}
    >
      <Grid style={{ display: "flex", flexDirection: "row" }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            borderRadius: 2,
            border: "1px solid #EAEBEB",
            pl: 1,
            pr: 1,
          }}
        >
          {data.map(({ name, code }) => (
            <Box
              sx={{
                cursor: "pointer",
                paddingLeft: "24px",
                paddingRight: "24px",
                paddingTop: "8px",
                paddingBottom: "8px",
                borderBottom:
                  selected === code
                    ? `4px solid ${theme.palette.primary.main}`
                    : "none",
              }}
              onClick={() => navigate(REACT_DATA_ACCESS_CONTROL + code)}
            >
              <Typography>{name}</Typography>
            </Box>
          ))}
        </Box>
      </Grid>
    </Grid>
  );
}

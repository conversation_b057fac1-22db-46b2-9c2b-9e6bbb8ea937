import api from "../configurations/http-common"

export const getCountry = async () => {
    return api
    .securedAxios()
    .get("web/countries");
}

export const createCountry = async (payload) => {
    return api
    .securedAxios()
    .post("web/country", payload);
}

export const filterCountry = async (pageNumber, pageSize, name, code) => {
    let query = "";
    if(name) {
        query += "&name=" + name;
    }
    if(code) {
        query += "&code=" + code;
    }
    if(pageNumber !== null || pageNumber !== "") {
        query += "&pageNumber=" + pageNumber;
    }
    if(pageSize !== null || pageSize !== "") {
        query += "&pageSize=" + pageSize;
    }
    return api
    .securedAxios()
    .get("web/countries/list?" + query);
}

export const getCountryById = async (id) => {
    return api
    .securedAxios()
    .get("web/country" + "?id=" + id);
}

export const updateCountry = async (payload) => {
    return api
    .securedAxios()
    .put("web/country", payload);
}

export const deleteCountry = async (id) => {
    return api
    .securedAxios()
    .delete("web/country" + "?id=" + id);
}
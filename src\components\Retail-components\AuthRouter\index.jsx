import { useRoutes } from "react-router-dom";
import Auth from "./auth";
import { onRouteBefore } from "./onRouteBefore";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
function AuthRouter({ routes }) {
  const location = useLocation();
  const { pathname } = location;
  const navigate = useNavigate();

  if (pathname === "/") {
    navigate("/dashboard");
  }
  const auth = new Auth({
    routes,
    onRouteBefore,
  });
  const reactRoutes = auth.transformRoutes();
  const elements = useRoutes(reactRoutes);
  return elements;
}

export default AuthRouter;

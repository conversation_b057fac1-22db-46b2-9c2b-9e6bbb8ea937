import { Box, Grid } from "@mui/material";
import React, { useState } from "react";
import Sidebar from "../Sidebar";
import AppContext from "../../context/AppContext";
import BottomNavigationBar from "../../components/layout-components/BottomNavigationBar";

export default function MainLayout(props) {
  const [tokenRefreshTimer, setTokenRefreshTimer] = useState(-1);
  const [selectedClient, setSelectedClient] = useState({});
  const value = {
    tokenRefreshTimer,
    selectedClient,
    setSelectedClient,
    setTokenRefreshTimer,
  };

  return (
    <AppContext.Provider value={value}>
      <Grid
        style={{
          width: "100%",
          height: "100%",
          overflow: "hidden",
          backgroundColor: "#f7fbfe",
        }}
      >
        <Grid style={{ height: "100%", display: "flex", flexDirection: "row" }}>
          {/* <Sidebar /> */}
          <Box sx={{ display: { xs: "none", lg: "block" } }}>
            <Sidebar />
          </Box>
          <Box sx={{ display: { xs: "block", lg: "none" } }}>
            <BottomNavigationBar />
          </Box>

          <Grid
            container
            mx={2}
            style={{
              height: "100%",
              overflowY: "auto",
              display: "block",
              width: "-webkit-fill-available",
            }}
          >
            <Box
              my={2}
              mx={0}
              height={"95%"}
              fullWidth={true}
              sx={{ pt: { xs: "30px", md: 0 } }}
            >
              {props.children}
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </AppContext.Provider>
  );
}

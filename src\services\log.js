import api from "../configurations/http-common";
export const getAuthorList = async () => {
    return api.securedAxios().get("/web/roleResource/detail");
};

export const getOperationLog = async (payload) => {
    return api.securedAxios().get("/web/oper_log/page", {
        params: payload,
    });
};


export const getLoginLog = async (payload) => {
    return api.securedAxios().get("/web/login_log/page", {
        params: payload,
    });
}


export const getOperationLogDetail = async (id) => {
    return api.securedAxios().get(`/web/oper_log/${id}`);
}
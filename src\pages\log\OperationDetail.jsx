import React from "react";
import { getOperationLogDetail } from "@/services/log";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Info as InfoIcon,
  Code as CodeIcon,
  Comment as CommentIcon,
  Error as ErrorIcon,
  Person as PersonIcon,
  Computer as ComputerIcon,
  Schedule as ScheduleIcon,
  Language as LanguageIcon,
} from "@mui/icons-material";
import {
  Button,
  Box,
  Typography,
  Divider,
  Paper,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Chip,
  Stack,
} from "@mui/material";
import dayjs from "dayjs";

function OperationDetail() {
  const { state } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [logData, setLogData] = React.useState([]);

  React.useEffect(() => {
    getOperationLogDetail(state?.id).then((res) => {
      setLogData(res?.data?.data);
    });
  }, []);

  const getModuleName = (code) => {
    const moduleKey = `operation_log.modules.${code}`;
    const translatedModule = t(moduleKey);
    return translatedModule !== moduleKey ? translatedModule : code;
  };

  const InfoCard = ({ icon, title, children }) => (
    <Card elevation={1} sx={{ height: "100%" }}>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={1} mb={2}>
          {icon}
          <Typography variant="h6" color="primary">
            {title}
          </Typography>
        </Stack>
        {children}
      </CardContent>
    </Card>
  );

  const InfoItem = ({ label, value, tooltip = false }) => (
    <Box mb={2}>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {label}
      </Typography>
      {tooltip ? (
        <Tooltip title={value} arrow>
          <Typography variant="body1" noWrap>
            {value || "-"}
          </Typography>
        </Tooltip>
      ) : (
        <Typography variant="body1">{value || "-"}</Typography>
      )}
    </Box>
  );

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: "auto" }}>
      {/* Header */}
      <Paper
        elevation={2}
        sx={{ p: 3, mb: 3, bgcolor: "primary.main", color: "white" }}>
        <Typography variant="h4" gutterBottom>
          {t("operation_log.operation_detail")}
        </Typography>
        <Typography variant="subtitle1">{t(logData.i18nName)}</Typography>
      </Paper>

      <Grid container spacing={3}>
        {/* User Information */}
        <Grid item xs={12} md={6}>
          <InfoCard
            icon={<PersonIcon color="primary" />}
            title={t("operation_log.user_info")}>
            <InfoItem
              label={t("operation_log.operator")}
              value={logData.operName}
              tooltip
            />
            <InfoItem
              label={t("operation_log.operator_id")}
              value={logData.userId}
            />
            <InfoItem
              label={t("operation_log.department_info")}
              value={`${logData.departmentId || "-"} / ${
                logData.departmentCode || "-"
              }`}
            />
          </InfoCard>
        </Grid>

        {/* System Information */}
        <Grid item xs={12} md={6}>
          <InfoCard
            icon={<ComputerIcon color="primary" />}
            title={t("operation_log.system_info")}>
            <InfoItem
              label={t("operation_log.host_ip")}
              value={logData.ipAddr}
            />
            <InfoItem
              label={t("operation_log.operation_location")}
              value={logData.location}
              tooltip
            />
            <InfoItem
              label={t("operation_log.os_browser")}
              value={`${logData.os} / ${logData.browser}`}
            />
          </InfoCard>
        </Grid>

        {/* Time Information */}
        <Grid item xs={12} md={6}>
          <InfoCard
            icon={<ScheduleIcon color="primary" />}
            title={t("operation_log.time_info")}>
            <InfoItem
              label={t("operation_log.operation_time")}
              value={dayjs(logData.operationTime).format("YYYY-MM-DD HH:mm")}
            />
            <InfoItem
              label={t("operation_log.execute_time")}
              value={`${logData?.executeTime}ms`}
            />
            <Box mb={2}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t("operation_log.operation_status")}
              </Typography>
              <Chip
                label={logData.status || "-"}
                color={logData.status === "success" ? "success" : "default"}
                size="small"
              />
            </Box>
          </InfoCard>
        </Grid>

        {/* Request Information */}
        <Grid item xs={12} md={6}>
          <InfoCard
            icon={<LanguageIcon color="primary" />}
            title={t("operation_log.request_info")}>
            <InfoItem
              label={t("operation_log.request_url")}
              value={logData.url}
              tooltip
            />
            <InfoItem
              label={t("operation_log.request_method")}
              value={logData.requestMethod}
            />
            <InfoItem
              label={t("operation_log.method_name")}
              value={logData.method}
              tooltip
            />
            <InfoItem
              label={t("operation_log.app_module")}
              value={`${logData.appCode} / ${getModuleName(
                logData.moduleCode
              )}`}
            />
            <Box mb={2}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t("operation_log.operation_type")}
              </Typography>
              <Chip
                label={logData.operatorType || "-"}
                variant="outlined"
                size="small"
              />
            </Box>
            <Box mb={2}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t("operation_log.business_type")}
              </Typography>
              <Chip
                label={logData.businessType || "-"}
                variant="outlined"
                size="small"
              />
            </Box>
          </InfoCard>
        </Grid>

        {/* Operation Information */}
        <Grid item xs={12}>
          <Card elevation={1}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                <CommentIcon color="primary" />
                <Typography variant="h6" color="primary">
                  {t("operation_log.operation_info")}
                </Typography>
              </Stack>
              <Paper variant="outlined" sx={{ p: 2, bgcolor: "grey.50" }}>
                <Typography variant="body1">{t(logData.i18nName)}</Typography>
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Request Parameters */}
        <Grid item xs={12}>
          <Card elevation={1}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                <CodeIcon color="primary" />
                <Typography variant="h6" color="primary">
                  {t("operation_log.request_params")}
                </Typography>
              </Stack>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  bgcolor: "grey.50",
                  maxHeight: 300,
                  overflow: "auto",
                }}>
                <Typography
                  component="pre"
                  variant="body2"
                  sx={{
                    fontFamily: "monospace",
                    whiteSpace: "pre-wrap",
                    wordBreak: "break-all",
                  }}>
                  {logData.param
                    ? JSON.stringify(JSON.parse(logData.param), null, 2)
                    : "-"}
                </Typography>
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Error Stack */}
        <Grid item xs={12}>
          <Card elevation={1}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                <ErrorIcon color="error" />
                <Typography variant="h6" color="primary">
                  {t("operation_log.error_stack")}
                </Typography>
                {!logData.errorStack && (
                  <Chip
                    label={t("operation_log.no_error_info")}
                    size="small"
                    color="success"
                    variant="outlined"
                  />
                )}
              </Stack>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  bgcolor: logData.errorStack ? "error.light" : "grey.50",
                  maxHeight: 300,
                  overflow: "auto",
                }}>
                <Typography
                  component="pre"
                  variant="body2"
                  sx={{
                    fontFamily: "monospace",
                    whiteSpace: "pre-wrap",
                    color: logData.errorStack ? "error.dark" : "text.primary",
                  }}>
                  {logData.errorStack || t("operation_log.no_error_message")}
                </Typography>
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Actions */}
        <Grid item xs={12}>
          <Box display="flex" justifyContent="center" mt={2}>
            <Button
              onClick={() => navigate(-1)}
              variant="contained"
              size="large"
              sx={{ minWidth: 120 }}>
              {t("operation_log.close")}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
}

export default OperationDetail;

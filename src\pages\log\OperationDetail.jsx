import React from "react";
import { getOperationLogDetail } from "@/services/log";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Info as InfoIcon,
  Code as CodeIcon,
  Comment as CommentIcon,
  Error as ErrorIcon,
  Person as PersonIcon,
  Computer as ComputerIcon,
  Schedule as ScheduleIcon,
  Language as LanguageIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  ArrowBack as ArrowBackIcon,
  ContentCopy as ContentCopyIcon,
  Visibility as VisibilityIcon,
} from "@mui/icons-material";
import {
  Button,
  Box,
  Typography,
  Divider,
  Paper,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Chip,
  Stack,
  Container,
  IconButton,
  Alert,
  Collapse,
  useTheme,
  alpha,
} from "@mui/material";
import dayjs from "dayjs";

function OperationDetail() {
  const { state } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const theme = useTheme();
  const [logData, setLogData] = React.useState({});
  const [showRequestParams, setShowRequestParams] = React.useState(false);
  const [showErrorStack, setShowErrorStack] = React.useState(false);

  React.useEffect(() => {
    getOperationLogDetail(state?.id).then((res) => {
      setLogData(res?.data?.data || {});
    });
  }, []);

  const getModuleName = (code) => {
    const moduleKey = `operation_log.modules.${code}`;
    const translatedModule = t(moduleKey);
    return translatedModule !== moduleKey ? translatedModule : code;
  };

  const getStatusColor = (status) => {
    return status == "0" ? "success" : "error";
  };

  const getStatusIcon = (status) => {
    return status == "0" ? <CheckCircleIcon /> : <CancelIcon />;
  };

  const getStatusText = (status) => {
    return status == "0"
      ? t("operation_log.status_success")
      : t("operation_log.status_failed");
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // 这里可以添加成功提示
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };

  // 创建一个更现代化的信息卡片组件
  const InfoCard = ({ icon, title, children, sx = {} }) => (
    <Card
      elevation={0}
      sx={{
        height: "100%",
        border: `1px solid ${alpha(theme.palette.primary.main, 0.12)}`,
        borderRadius: 2,
        transition: "all 0.2s ease-in-out",
        "&:hover": {
          boxShadow: theme.shadows[4],
          borderColor: alpha(theme.palette.primary.main, 0.25),
        },
        ...sx,
      }}>
      <CardContent sx={{ p: 3 }}>
        <Stack direction="row" alignItems="center" spacing={1.5} mb={3}>
          <Box
            sx={{
              p: 1,
              borderRadius: 1.5,
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}>
            {icon}
          </Box>
          <Typography variant="h6" fontWeight={600} color="text.primary">
            {title}
          </Typography>
        </Stack>
        {children}
      </CardContent>
    </Card>
  );

  // 创建一个更美观的信息项组件
  const InfoItem = ({ label, value, tooltip = false, copyable = false }) => (
    <Box mb={2.5}>
      <Typography
        variant="body2"
        color="text.secondary"
        gutterBottom
        sx={{ fontWeight: 500, mb: 0.5 }}>
        {label}
      </Typography>
      <Box display="flex" alignItems="center" gap={1}>
        {tooltip ? (
          <Tooltip title={value} placement="top">
            <Typography
              variant="body1"
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                cursor: "pointer",
                flex: 1,
                p: 1.5,
                bgcolor: alpha(theme.palette.grey[500], 0.05),
                borderRadius: 1,
                border: `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
              }}>
              {value || "-"}
            </Typography>
          </Tooltip>
        ) : (
          <Typography
            variant="body1"
            sx={{
              flex: 1,
              p: 1.5,
              bgcolor: alpha(theme.palette.grey[500], 0.05),
              borderRadius: 1,
              border: `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
            }}>
            {value || "-"}
          </Typography>
        )}
        {copyable && value && (
          <IconButton
            size="small"
            onClick={() => copyToClipboard(value)}
            sx={{
              color: theme.palette.text.secondary,
              "&:hover": { color: theme.palette.primary.main },
            }}>
            <ContentCopyIcon fontSize="small" />
          </IconButton>
        )}
      </Box>
    </Box>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 页面头部 */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${
            theme.palette.primary.main
          } 0%, ${alpha(theme.palette.primary.main, 0.8)} 100%)`,
          color: "white",
          position: "relative",
          overflow: "hidden",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            right: 0,
            width: "200px",
            height: "200px",
            background: alpha("#fff", 0.1),
            borderRadius: "50%",
            transform: "translate(50%, -50%)",
          },
        }}>
        <Stack direction="row" alignItems="center" spacing={2} mb={2}>
          <IconButton
            onClick={() => navigate(-1)}
            sx={{
              color: "white",
              bgcolor: alpha("#fff", 0.2),
              "&:hover": { bgcolor: alpha("#fff", 0.3) },
            }}>
            <ArrowBackIcon />
          </IconButton>
          <Box>
            <Typography variant="h4" fontWeight={700} gutterBottom>
              {t("operation_log.detail_title")}
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9, fontWeight: 400 }}>
              {t(logData.i18nName) || t("operation_log.operation_info")}
            </Typography>
          </Box>
        </Stack>

        {/* 状态指示器 */}
        <Box mt={3}>
          <Chip
            icon={getStatusIcon(logData.status)}
            label={getStatusText(logData.status)}
            color={getStatusColor(logData.status)}
            variant="filled"
            sx={{
              bgcolor: alpha("#fff", 0.2),
              color: "white",
              fontWeight: 600,
              fontSize: "16px",
              "& .MuiChip-icon": { color: "white" },
            }}
          />
        </Box>
      </Paper>

      <Grid container spacing={4}>
        {/* 操作人员信息 */}
        <Grid item xs={12} md={6}>
          <InfoCard icon={<PersonIcon />} title={t("operation_log.operator")}>
            <InfoItem
              label={t("operation_log.operator")}
              value={logData.operName}
              copyable
            />
            <InfoItem
              label={t("operation_log.operator_id")}
              value={logData.userId}
              copyable
            />
            <InfoItem
              label={t("operation_log.department_info")}
              value={`${logData.departmentId || "-"} / ${
                logData.departmentCode || "-"
              }`}
            />
            <InfoItem
              label={t("operation_log.tenant_code")}
              value={logData.tenantCode}
            />
          </InfoCard>
        </Grid>

        {/* 系统信息 */}
        <Grid item xs={12} md={6}>
          <InfoCard
            icon={<ComputerIcon />}
            title={t("operation_log.system_info")}>
            <InfoItem
              label={t("operation_log.host_ip")}
              value={logData.ipAddr}
              copyable
            />
            <InfoItem
              label={t("operation_log.operation_location")}
              value={logData.location}
              tooltip
            />
            <InfoItem
              label={t("operation_log.os_browser")}
              value={`${logData.os} / ${logData.browser}`}
            />
          </InfoCard>
        </Grid>

        {/* 时间信息 */}
        <Grid item xs={12} md={6}>
          <InfoCard
            icon={<ScheduleIcon />}
            title={t("operation_log.time_info")}>
            <InfoItem
              label={t("operation_log.operation_time")}
              value={dayjs(logData.operationTime).format("YYYY-MM-DD HH:mm:ss")}
            />
            <InfoItem
              label={t("operation_log.execute_time")}
              value={`${logData?.executeTime || 0} ms`}
            />
            <Box mb={2.5}>
              <Typography
                variant="body2"
                color="text.secondary"
                gutterBottom
                sx={{ fontWeight: 500, mb: 0.5 }}>
                {t("operation_log.operation_status")}
              </Typography>
              <Chip
                icon={getStatusIcon(logData.status)}
                label={getStatusText(logData.status)}
                color={getStatusColor(logData.status)}
                variant="outlined"
                sx={{ fontWeight: 600, fontSize: "16px" }}
              />
            </Box>
          </InfoCard>
        </Grid>

        {/* 请求信息 */}
        <Grid item xs={12} md={6}>
          <InfoCard
            icon={<LanguageIcon />}
            title={t("operation_log.request_info")}>
            <InfoItem
              label={t("operation_log.request_url")}
              value={logData.url}
              tooltip
              copyable
            />
            <InfoItem
              label={t("operation_log.request_method")}
              value={logData.requestMethod}
            />
            <InfoItem
              label={t("operation_log.method_name")}
              value={logData.method}
              tooltip
              copyable
            />
            <InfoItem
              label={t("operation_log.app_module")}
              value={`${logData.appCode} / ${getModuleName(
                logData.moduleCode
              )}`}
            />

            {/* 操作类型和业务类型 */}
            <Stack direction="row" spacing={2} mb={2.5}>
             
         
            </Stack>
          </InfoCard>
        </Grid>

        {/* 操作信息 */}
        <Grid item xs={12}>
          <InfoCard
            icon={<CommentIcon />}
            title={t("operation_log.operation_info")}>
            <Paper
              variant="outlined"
              sx={{
                p: 3,
                bgcolor: alpha(theme.palette.info.main, 0.05),
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                borderRadius: 2,
              }}>
              <Typography
                variant="body1"
                sx={{
                  fontWeight: 500,
                  color: theme.palette.text.primary,
                  lineHeight: 1.6,
                }}>
                {t(logData.i18nName) || t("operation_log.operation_info")}
              </Typography>
            </Paper>
          </InfoCard>
        </Grid>

        {/* 请求参数 */}
        <Grid item xs={12}>
          <InfoCard
            icon={<CodeIcon />}
            title={t("operation_log.request_params")}>
            <Box>
              <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                <Button
                  size="small"
                  variant="outlined"
                  startIcon={<VisibilityIcon />}
                  onClick={() => setShowRequestParams(!showRequestParams)}
                  sx={{ textTransform: "none" }}>
                  {showRequestParams ? "隐藏参数" : "显示参数"}
                </Button>
                {logData.requestParam && (
                  <IconButton
                    size="small"
                    onClick={() => copyToClipboard(logData.requestParam)}
                    sx={{
                      color: theme.palette.text.secondary,
                      "&:hover": { color: theme.palette.primary.main },
                    }}>
                    <ContentCopyIcon fontSize="small" />
                  </IconButton>
                )}
              </Stack>

              <Collapse in={showRequestParams}>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    bgcolor: alpha(theme.palette.grey[900], 0.05),
                    maxHeight: 300,
                    overflow: "auto",
                    borderRadius: 2,
                  }}>
                  <Typography
                    component="pre"
                    variant="body2"
                    sx={{
                      fontFamily: "Consolas, Monaco, 'Courier New', monospace",
                      whiteSpace: "pre-wrap",
                      fontSize: "0.875rem",
                      lineHeight: 1.5,
                      color: theme.palette.text.primary,
                    }}>
                    {logData.requestParam || logData.param
                      ? JSON.stringify(
                          JSON.parse(logData.requestParam || logData.param),
                          null,
                          2
                        )
                      : "无请求参数"}
                  </Typography>
                </Paper>
              </Collapse>
            </Box>
          </InfoCard>
        </Grid>

        {/* 错误堆栈信息 */}
        <Grid item xs={12}>
          <InfoCard
            icon={<ErrorIcon />}
            title={t("operation_log.error_stack")}
            sx={{
              borderColor: logData.errorStack
                ? alpha(theme.palette.error.main, 0.3)
                : alpha(theme.palette.success.main, 0.3),
            }}>
            <Box>
              {/* 状态指示 */}
              <Box mb={2}>
                {logData.errorStack ? (
                  <Alert severity="error" variant="outlined" sx={{ mb: 2 }}>
                    检测到错误信息
                  </Alert>
                ) : (
                  <Alert severity="success" variant="outlined" sx={{ mb: 2 }}>
                    {t("operation_log.no_error_message")}
                  </Alert>
                )}
              </Box>

              {/* 错误详情 */}
              {logData.errorStack && (
                <Box>
                  <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<VisibilityIcon />}
                      onClick={() => setShowErrorStack(!showErrorStack)}
                      sx={{ textTransform: "none" }}>
                      {showErrorStack ? "隐藏错误详情" : "显示错误详情"}
                    </Button>
                    <IconButton
                      size="small"
                      onClick={() => copyToClipboard(logData.errorStack)}
                      sx={{
                        color: theme.palette.text.secondary,
                        "&:hover": { color: theme.palette.error.main },
                      }}>
                      <ContentCopyIcon fontSize="small" />
                    </IconButton>
                  </Stack>

                  <Collapse in={showErrorStack}>
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 2,
                        bgcolor: alpha(theme.palette.error.main, 0.05),
                        border: `1px solid ${alpha(
                          theme.palette.error.main,
                          0.2
                        )}`,
                        maxHeight: 300,
                        overflow: "auto",
                        borderRadius: 2,
                      }}>
                      <Typography
                        component="pre"
                        variant="body2"
                        sx={{
                          fontFamily:
                            "Consolas, Monaco, 'Courier New', monospace",
                          whiteSpace: "pre-wrap",
                          fontSize: "0.875rem",
                          lineHeight: 1.5,
                          color: theme.palette.error.dark,
                        }}>
                        {logData.errorStack}
                      </Typography>
                    </Paper>
                  </Collapse>
                </Box>
              )}
            </Box>
          </InfoCard>
        </Grid>

        {/* 操作按钮 */}
        <Grid item xs={12}>
          <Paper
            elevation={0}
            sx={{
              p: 3,
              mt: 2,
              bgcolor: alpha(theme.palette.grey[50], 0.5),
              borderRadius: 2,
              textAlign: "center",
            }}>
            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                onClick={() => navigate(-1)}
                variant="contained"
                size="large"
                startIcon={<ArrowBackIcon />}
                sx={{
                  minWidth: 140,
                  borderRadius: 2,
                  textTransform: "none",
                  fontWeight: 600,
                }}>
                {t("operation_log.close")}
              </Button>
            </Stack>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OperationDetail;

/**
 * @Description: 组件工具函数类
 */
import React from "react";
import { Navigate } from "react-router-dom";
import Guard from "./guard";

export default class Fn {
  routes;
  onRouteBefore;

  constructor(option) {
    this.routes = option.routes || [];
    this.onRouteBefore = option.onRouteBefore;
  }

  /**
   * @description: 路由配置列表数据转换
   * @param {string} redirect 要重定向的路由路径
   * @param {function} component 函数形式import懒加载组件
   * @param {object} meta 自定义字段
   */
  transformRoutes(routeList = this.routes) {
    const list = [];
    routeList.forEach((route) => {
      const obj = { ...route };
      if (obj.path === undefined) {
        return;
      }
      

      if(obj.hide){
        return;
      }

      if (obj.redirect) {
        obj.element = <Navigate to={obj.redirect} replace={true} />;
      } else if (obj.element) {
        let meta = {
          id: obj.id,
          ...(obj.meta || {}),
        };
        obj.element = (
          <Guard
            element={obj.element}
            meta={meta}
            onRouteBefore={this.onRouteBefore}
          />
        );
      }
      delete obj.redirect;
      if (obj.children) {
        obj.children = this.transformRoutes(obj.children);
      }
      list.push(obj);
    });
    return list;
  }
}

import { styled } from "@mui/material/styles";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Button from "@mui/material/Button";
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

const CmptecoModal = ({
  handleClose = () => {},
  handleSubmit = () => {},
  title = "",
  showClose = true,
  showAction = true,
  children = null,
  open = false,
  cancleText = "取消",
  okText = "确定",

  footer = null,
}) => {
  return (
    <BootstrapDialog
      onClose={handleClose}
      aria-labelledby="customized-dialog-title"
      open={open}
      maxWidth={2000}
    >
      {title && (
        <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
          {title}
        </DialogTitle>
      )}
      {showClose && (
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      )}
      <DialogContent
        sx={{
          minWidth: "500px",
        }}
        dividers
      >
        {children}
      </DialogContent>

      {footer}

      {showAction && (
        <DialogActions>
          <Button autoFocus onClick={handleClose}>
            {cancleText || "取消"}
          </Button>
          <Button autoFocus onClick={handleSubmit}>
            {okText || "确定"}
          </Button>
        </DialogActions>
      )}
    </BootstrapDialog>
  );
};

export default CmptecoModal;

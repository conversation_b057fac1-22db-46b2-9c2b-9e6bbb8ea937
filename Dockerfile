# Use an official Node.js image as the base
FROM node:18-alpine AS build

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json to the container
COPY package*.json ./

# Install dependencies
RUN npm install --force && npm install react-scripts@5.0.1 --force

# Copy the rest of the application code to the container
COPY . .

# Set the environment variable for production
ENV NODE_ENV=production

ENV REACT_APP_CMS_URL=http://************:8999/#/redirect

ENV REACT_APP_AIRETAIL_URL=http://************:8999/ai-dashboard

ENV REACT_APP_EPTAG_URL=http://************:8884
# Set the environment variable for backend server
#ENV REACT_APP_SERVER_URL=https://level-middleware.zkdigimaxdev.com
ENV REACT_APP_SERVER_URL=http://************:9000

# Build the React application
RUN npm run build

# Use a lightweight Nginx image for the final stage
FROM nginx:alpine

# Copy Nginx configuration file
COPY nginx.conf /etc/nginx/nginx.conf

# Copy the built application from the previous stage
COPY --from=build /app/build /usr/share/nginx/html

# Expose the desired port
EXPOSE 8999

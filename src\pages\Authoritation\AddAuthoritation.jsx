import {
  Box,
  Button,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Grid,
  TextField,
  CircularProgress,
} from "@mui/material";
import { REACT_AUTHORITATION } from "@/router/ReactEndPoints";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import ListLayout from "@/components/ListLayout";
import { useTranslation } from "react-i18next";
import AccessControl from "../../components/AccessControl.jsx";
import CommonUtil from "@/util/CommonUtils";
import { useSnackbar } from "notistack";
import { getResourceList, addRoleData } from "../../services/authoritation.js";
import { handleCheckboxChange } from "./checkBox.js";
function AddAuthoritation() {
  const { t } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(false);
  const [menus, setMenus] = useState([]);
  const [authName, setAuthName] = useState("");
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const navigate = useNavigate();
  useEffect(() => {
    getResourceList().then((res) => {
      setMenus(res?.data?.data);
    });
  }, []);

  const changeFun = (values, isParent, type) => {
    handleCheckboxChange(menus, setSelectedPermissions, values, isParent, type);
  };

  //新增角色
  const handleSubmit = () => {
    let params = {
      name: authName,
      resourceIds: selectedPermissions,
    };

    if (selectedPermissions === undefined) {
      enqueueSnackbar("The allocation permission cannot be empty.", {
        variant: "warning",
      });
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    if (!CommonUtil.isEmpty(params.name)) {
      addRoleData(params).then((res) => {
        if (res.data.code === "LVLI0000") {
          enqueueSnackbar(t("PCS84"), {
            variant: "success",
          });
          navigate(REACT_AUTHORITATION);
        } else {
          enqueueSnackbar(res?.data?.message, {
            variant: "error",
          });
        }
      });
      setIsLoading(false);
    } else {
      enqueueSnackbar(t("PCS85"), {
        variant: "warning",
      });
      setIsLoading(false);
    }
  };

  return (
    <>
      {isLoading ? (
        <CircularProgress />
      ) : (
        <ListLayout navigateBack={REACT_AUTHORITATION} title={t("LVLDB0036")}>
          <Grid>
            <Grid
              display={"flex"}
              sx={{
                background: "#FFF",
                height: "100px",
                borderRadius: "15px",
              }}
            >
              <Box
                sx={{
                  margin: "30px 30px",
                }}
              >
                <TextField
                  size="small"
                  label={t("PCS75")}
                  placeholder={t("PCS75")}
                  value={authName}
                  onChange={(e) => setAuthName(e.target.value)}
                ></TextField>
              </Box>
            </Grid>

            {menus?.map((menu) => (
              <Grid key={menu.id} pt={1.5}>
                <Card
                  elevation={0}
                  sx={{
                    background: "#FFFFFF 0% 0% no-repeat padding-box",
                    fontWeight: "bold",
                    borderRadius: "8px",
                  }}
                >
                  <FormGroup
                    sx={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      ml: 4, // 设置左边距
                    }}
                  >
                    <Grid lg={2}>
                      <FormControlLabel
                        key={menu.id}
                        control={
                          <Checkbox
                            id={`permission-checkbox-${menu.id}`}
                            checked={selectedPermissions?.includes(menu.id)}
                            onChange={() =>
                              changeFun(menu.id, menu.parentId, menu.type)
                            }
                          />
                        }
                        label={menu.name}
                        sx={{
                          textAlign: "left",
                          font: "normal normal medium 28px/38px Roboto",
                          fontWeight: "bold",
                          letterSpacing: "0px",
                          color: "#474B4F",
                        }}
                      />
                    </Grid>
                  </FormGroup>
                  <CardContent>
                    {menu?.resourceList.map((item) => (
                      <Grid
                        key={item.id}
                        sx={{
                          textAlign: "left",
                          font: "normal normal medium 32px/38px Roboto",
                          letterSpacing: "0px",
                          color: "#474B4F",
                        }}
                      >
                        <FormGroup
                          sx={{
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "center",
                            ml: 6, // 设置左边距
                          }}
                        >
                          <Grid lg={2}>
                            {item.name !== null ? (
                              <FormControlLabel
                                key={item.id}
                                control={
                                  <Checkbox
                                    id={`permission-checkbox-${item.id}`}
                                    checked={selectedPermissions?.includes(
                                      item.id
                                    )}
                                    onChange={() =>
                                      changeFun(
                                        item.id,
                                        item.parentId,
                                        item.type
                                      )
                                    }
                                  />
                                }
                                label={item.name}
                                sx={{
                                  textAlign: "left",
                                  font: "normal normal medium 18px/24px Roboto",
                                  fontWeight: "bold",
                                  letterSpacing: "0px",

                                  color: "#474B4F",
                                }}
                              />
                            ) : null}
                          </Grid>
                        </FormGroup>

                        <FormGroup
                          sx={{
                            display: "flex",
                            flexDirection: "row",
                            flexWrap: "wrap",
                            ml: 12,
                          }}
                        >
                          {item?.resourceList.map((permission) => (
                            <Grid lg={2}>
                              <FormControlLabel
                                key={permission.id}
                                control={
                                  <Checkbox
                                    id={`permission-checkbox-${permission.id}`}
                                    checked={selectedPermissions?.includes(
                                      permission.id
                                    )}
                                    onChange={() =>
                                      changeFun(
                                        permission.id,
                                        permission.parentId,
                                        permission.type
                                      )
                                    }
                                  />
                                }
                                label={permission.name}
                                sx={{
                                  width: 200,
                                  mt: 1,
                                }}
                              />
                            </Grid>
                          ))}
                        </FormGroup>
                      </Grid>
                    ))}
                  </CardContent>
                </Card>
              </Grid>
            ))}

            <Box display={"flex"} flexDirection={"row-reverse"} marginTop={5}>
              <Box item pl={2}>
                <AccessControl authorization={"1051"}>
                  <Button variant="contained" onClick={handleSubmit}>
                    {t("LVL0013")}
                  </Button>
                </AccessControl>
              </Box>
              <Box item>
                <Button
                  id="AddAuthorizationLevel-button-02"
                  className="text-transform-none"
                  variant="outlined"
                  onClick={() => navigate(REACT_AUTHORITATION)}
                  size="large"
                >
                  {t("LVLRC0017")}
                </Button>
              </Box>
            </Box>
          </Grid>
        </ListLayout>
      )}
    </>
  );
}

export default AddAuthoritation;

import { tableI18n } from "@/util/tableLang";
import { dateFormat } from "@/util/utils";
// material-ui
import { styled } from "@mui/material/styles";
import { Box, Grid, Switch, Paper } from "@mui/material";
import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";

import {
  MaterialReactTable,
  MRT_ToggleFullScreenButton,
  MRT_ShowHideColumnsButton,
  MRT_TablePagination,
  MRT_ToggleDensePaddingButton,
  useMaterialReactTable,
} from "material-react-table";
import { Typography } from "antd";

const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#ffffff",
    color: "#ffffff",
    padding: "10px",
    maxWidth: "90%",
    margin: "0px auto",
    fontSize: theme.typography.pxToRem(12),
    border: "1px solid #dadde9",
  },
}));

const CmptecoTable = (props) => {
  const {
    mrt = true,
    renderToolbarInternalActions = () => null, //头部右侧
    renderTopToolbarCustomActions = () => null, //头部左侧
    renderBottomToolbarCustomActions = () => null, //底部左侧工具
    enablePagination = true,
    columns,
    state,
    data,
    ...orther
  } = props;
  if (data && data.length > 0) {
    if (state && state.isLoading !== undefined) {
      state.isLoading = state.isLoading;
      state.showProgressBars = state.isLoading;
    }
  } else if (state) {
    state.showProgressBars = false;
  }

  let columnsConfig = columns?.map((item) => {
    if (!item.enableColumnActions) {
      item.enableColumnActions = false;
    }
    if (!item.enableSorting) {
      item.enableSorting = false;
    }
    if (item.format) {
      item.Cell = ({ cell, row }) => {
        let value = row.original[item.accessorKey];
        if (value === null || value === "" || value === undefined) {
          return "-";
        }
        let type = typeof item.format;
        if (type === "boolean") {
          try {
            let value = row.original[item.accessorKey];
            let time = new Date(value).getTime();
            return dateFormat(time);
          } catch (error) {
            return value;
          }
        } else if (type === "function") {
          return item.format({ cell, row });
        } else if (type === "string") {
          return dateFormat(row.original[item.accessorKey], item.format);
        } else {
          return row.original[item.accessorKey];
        }
      };
    } else if (item.tooltip) {
      item.Cell = ({ cell, row }) => {
        let value = row.original[item.accessorKey];
        if (value === null || value === "" || value === undefined) {
          return "-";
        }

        if (value.length > item.tooltip) {
          return (
            <HtmlTooltip
              title={
                <Paper
                  sx={{
                    border: "none",
                    boxShadow: "none",
                  }}
                >
                  {value}
                </Paper>
              }
            >
              <Box
                sx={{
                  width: (item.size || 200) + "px",
                  overflow: "hidden",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                }}
              >
                {value}
              </Box>
            </HtmlTooltip>
          );
        } else {
          return value;
        }
      };
    } else if (item.switch) {
      item.Cell = ({ cell, row }) => {
        let value = row.original[item.accessorKey];
        if (value === null || value === "" || value === undefined) {
          return "-";
        }

        return (
          <>
            <Switch
              onChange={(e) => {
                e.stopPropagation();
                item.switch(row.original);
              }}
              checked={row.original[item.accessorKey]}
            />
          </>
        );
      };
    } else if (item.status) {
      item.Cell = ({ cell, row }) => {
        let value = row.original[item.accessorKey];
        if (value === null || value === "" || value === undefined) {
          return "-";
        }

        return (
          <>
            <Typography>{value === 0 ? "在线" : "离线"}</Typography>
          </>
        );
      };
    } else {
      if (!item.Cell) {
        item.Cell = ({ cell, row }) => {
          let value = row.original[item.accessorKey];
          if (value === null || value === "" || value === undefined) {
            return "-";
          }
          return value;
        };
      }
    }
    return item;
  });

  let defaultConfig = {
    // localization: tableI18n,
    enablePagination: false, //是否开启分页
    muiTablePaperProps: {
      elevation: 0,
      sx: {
        borderRadius: "5px",
        border: "1px solid #f0f0f0",
      },
    },
    muiTableHeadRowProps: {
      //表单头部颜色
      sx: {
        backgroundColor: "#fafafa",
        boxShadow: "none",
      },
    },
    muiPaginationProps: {
      //分页滚动条
      color: "secondary",
      rowsPerPageOptions: [10, 20, 30],
      shape: "rounded",
      variant: "outlined",
    },
    positionToolbarAlertBanner: "none",
    // 解决列太多宽度太长问题
    enableColumnResizing: true,
    manualFiltering: true,
    // 布局方式
    layoutMode: "table",
    // 固定头部
    enableStickyHeader: true,
    manualPagination: true,
    enableDensityToggle: true,
    manualSorting: true,

    // 设置背景颜色
    muiTableBodyCellProps: { sx: { backgroundColor: "white" } },

    muiTableProps: { sx: { backgroundColor: "white" } },

    muiBottomToolbarProps: { sx: { backgroundColor: "white" } },

    muiTopToolbarProps: { sx: { backgroundColor: "white" } },

    positionActionsColumn: "last",

    paginationDisplayMode: "pages",

    enableBottomToolbar: true, //页面底部

    displayColumnDefOptions: {
      "mrt-row-actions": {
        header: "action",
      },
    },
    initialState: {
      columnPinning: {
        right: ["mrt-row-actions"],
      },
    },
    ...orther,
    columns: columnsConfig || [],
    state,
    data: data || [],
    renderCaption: ({ table }) => {
      //分页栏上面添加一栏
      return null;
    },
    renderToolbarInternalActions: ({ table }) => (
      <Box
        sx={{
          display: "flex",
        }}
      >
        {renderToolbarInternalActions && (
          <Grid
            sx={{
              marginRight: "20px",
            }}
          >
            {renderToolbarInternalActions({ table })}
          </Grid>
        )}
        {mrt && (
          <>
            <MRT_ShowHideColumnsButton table={table} />
            <MRT_ToggleDensePaddingButton table={table} />
            <MRT_ToggleFullScreenButton table={table} />
          </>
        )}
      </Box>
    ),
    renderEmptyRowsFallback: ({ table }) => (
      <Box
        sx={{
          padding: "40px",
          display: "flex",
          justifyContent: "center",
          alignContent: "center",
        }}
      >
        {tableI18n.noRecordsToDisplay}
      </Box>
    ),
    // renderTopToolbarCustomActions: () => {
    //   return null;
    // },
    //Adding a custom button to the bottom toolbar
    renderBottomToolbarCustomActions: ({ table }) => {
      return (
        <Box
          sx={{
            alignItems: "center",
            boxSizing: "border-box",
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <Box>
            {renderBottomToolbarCustomActions &&
              renderBottomToolbarCustomActions({ table })}
          </Box>
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              position: "relative",
              right: 0,
              top: 0,
            }}
          >
            {enablePagination && (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-end",
                }}
              >
                <Grid sx={{ color: "#595959", marginRight: "10px" }}>
                  {tableI18n.total?.replace("{total}", String(props.rowCount))}
                  {/* 共 <span style={{ margin: "0px 6px" }}>{props.rowCount}</span>
                  条记录 */}
                </Grid>
                <MRT_TablePagination
                  sx={{
                    padding: "0",
                  }}
                  table={table}
                />
              </Box>
            )}
          </Box>
        </Box>
      );
    },
  };
  const table = useMaterialReactTable(defaultConfig);
  return <MaterialReactTable table={table} />;
};
export default CmptecoTable;

import React, { useState } from "react";
import {
  Grid,
  Box,
  TextField,
  Button,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
} from "@mui/material";
import { useTranslation } from "react-i18next";

function OpercationSearch({ onSearch, onReset }) {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useState({
    userName: "",
    ipAddr: "",
    // location: "",
    status: "",
  });

  const handleChange = (e) => {
    setSearchParams({ ...searchParams, [e.target.name]: e.target.value });
  };

  const handleSubmit = () => {
    onSearch(searchParams);
  };

  const handleReset = () => {
    setSearchParams({
      userName: "",
      ipAddr: "",
      // location: "",
      status: "",
    });
    onReset();
  };

  return (
    <React.Fragment>
      <Grid
        display={"flex"}
        sx={{
          width: "100%",
          background: "#FFF",
          height: "100px",
          borderRadius: "15px",
        }}>
        <Box sx={{ margin: "30px 30px" }}>
          <TextField
            size="small"
            label={t("operation_log.accountName")}
            placeholder={t("operation_log.accountNameTip")}
            value={searchParams.userName}
            name="userName"
            onChange={handleChange}
          />
        </Box>

        <Box sx={{ margin: "30px 30px" }}>
          <TextField
            size="small"
            label={t("operation_log.ipAddrName")}
            placeholder={t("operation_log.ipAddrNameTip")}
            value={searchParams.ipAddr}
            name="ipAddr"
            onChange={handleChange}
          />
        </Box>

        {/* <Box sx={{ margin: "30px 30px" }}>
          <TextField
            size="small"
            label={t("operation_log.addressName")}
            placeholder={t("operation_log.addressName")}
            value={searchParams.location}
            name="location"
            onChange={handleChange}
          />
        </Box> */}

        <Box sx={{ margin: "30px 30px", minWidth: 190 }}>
          <FormControl size="small" fullWidth>
            <InputLabel id="status-select-label">{t("LVLDAC0013")}</InputLabel>
            <Select
              labelId="status-select-label"
              id="status-select"
              value={searchParams.status}
              label={t("LVLDAC0013")}
              name="status"
              onChange={handleChange}>
              <MenuItem value="0">{t("common.common_success")}</MenuItem>
              <MenuItem value="1">{t("common.common_fail")}</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Box sx={{ margin: "35px 35px" }}>
          <Button
            variant="contained"
            sx={{ marginLeft: 1, p: 0.5 }}
            style={{ height: "28px" }}
            onClick={handleSubmit}>
            {t("common.common_search")}
          </Button>

          <Button
            style={{ height: "28px" }}
            sx={{ marginLeft: 1, p: 0.5 }}
            variant="outlined"
            onClick={handleReset}>
            {t("LVLDB0016")}
          </Button>
        </Box>
      </Grid>
    </React.Fragment>
  );
}

export default OpercationSearch;

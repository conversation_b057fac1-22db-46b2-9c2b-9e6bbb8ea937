import * as React from "react";
import { Box, Paper } from "@mui/material";
// import { ReactComponent as Logo } from "@/assets/images/l3-logo-small.svg";
import Logo from "@/assets/logo/New Zata Logo Name.png";
import { useTranslation } from "react-i18next";
import SidebarMenuItem from "@/layout/components/SidebarMenuItem";
import UserProfileMenu from "@/layout/leftMenu/UserProfileMenu";
import { ReactComponent as InActiveRetailClientMenu } from "@/assets/images/menu_retail_client.svg";
import { ReactComponent as ActiveRetailClientMenu } from "@/assets/images/menu_retail_client_active.svg";
import { ReactComponent as InActiveOutletMenu } from "@/assets/images/menu_outlet.svg";
import { ReactComponent as ActiveOutletMenu } from "@/assets/images/menu_outlet_active.svg";
import { ReactComponent as InActiveDeviceMenu } from "@/assets/images/menu_device.svg";
import { ReactComponent as ActiveDeviceMenu } from "@/assets/images/menu_device.svg";
import { ReactComponent as ActiveDashboardMenu } from "@/assets/images/menu_dashboard_in.svg";
import { ReactComponent as InActiveDashboardMenu } from "@/assets/images/menu_dashboard_out.svg";
import { ReactComponent as ActiveWorkshopMenu } from "@/assets/images/menu_workshop_in.svg";
import { ReactComponent as InActiveWorkshopMenu } from "@/assets/images/menu_workshop_out.svg";
import { ReactComponent as InActiveDataAccessMenu } from "@/assets/images/menu_data_access_control.svg";
import { ReactComponent as ActiveDataAccessMenu } from "@/assets/images/menu_data_access_control_active.svg";
import { ReactComponent as ActiveLocationMenu } from "@/assets/images/location_icon_in.svg";
import { ReactComponent as InActiveLocationMenu } from "@/assets/images/location_icon_out.svg";
import { ReactComponent as LanguageMenu } from "@/assets/images/menu_language.svg";
import { ReactComponent as authoritation } from "@/assets/images/authoritation.svg";
// import RefreshToken from "../../configurations/RefreshToken";

export default function BottomNavigationBar(props) {
  const { t } = useTranslation();
  const path = window.location.pathname;
  return (
    <>
      <Box
        component={Paper}
        elevation={0}
        variant="elevation"
        height={"60px"}
        width={"100%"}
        justifyContent={"flex-start"}
        bgcolor={"black"}
        position={"fixed"}
        top={0}
        zIndex={999}
        borderRadius={0}>
        <Box display={"flex"} height={"100%"} width={"100%"}>
          <Box p={2} flexGrow={1}>
            {/* <Logo /> */}
            <img src={Logo} height={60} width={200}></img>
          </Box>
          <Box item>
            <SidebarMenuItem
              link=""
              label="Language"
              isActive={false}
              activeMenu={LanguageMenu}
              inActiveMenu={LanguageMenu}
            />
          </Box>
          <Box item>
            <UserProfileMenu />
          </Box>
        </Box>
      </Box>
      <Box
        component={Paper}
        elevation={0}
        variant="elevation"
        height={"60px"}
        width={"100%"}
        justifyContent={"flex-start"}
        bgcolor={"black"}
        position={"fixed"}
        bottom={0}
        zIndex={999}
        borderRadius={0}>
        <Box display={"flex"} height={"100%"} width={"100%"}>
          <SidebarMenuItem
            link="/dashboard"
            label="Outlets Dashboard"
            isActive={path.includes("/dashboard")}
            activeMenu={ActiveDashboardMenu}
            inActiveMenu={InActiveDashboardMenu}
          />
          <SidebarMenuItem
            link="/location/country"
            label="Location"
            isActive={path.includes("/location")}
            activeMenu={ActiveLocationMenu}
            inActiveMenu={InActiveLocationMenu}
          />
          <SidebarMenuItem
            link="/authoritation"
            label="Authoritation"
            isActive={path.includes("/authoritation")}
            activeMenu={ActiveLocationMenu}
            inActiveMenu={InActiveLocationMenu}
          />
          <SidebarMenuItem
            link="/retail-client"
            label="Retail Client"
            isActive={path.includes("/retail-client")}
            activeMenu={ActiveRetailClientMenu}
            inActiveMenu={InActiveRetailClientMenu}
          />
          <SidebarMenuItem
            link="/outlet"
            label="Outlet"
            isActive={
              path.includes("/outlet") && !path.includes("/outlet_type")
            }
            activeMenu={ActiveOutletMenu}
            inActiveMenu={InActiveOutletMenu}
          />

          <SidebarMenuItem
            link="/device"
            label="Device"
            isActive={path.includes("/device")}
            activeMenu={ActiveDeviceMenu}
            inActiveMenu={InActiveDeviceMenu}
          />
          <SidebarMenuItem
            link="/data-access-control/Device"
            label="Data Access Control"
            isActive={path.includes("/data-access-control")}
            activeMenu={ActiveDataAccessMenu}
            inActiveMenu={InActiveDataAccessMenu}
          />
          <SidebarMenuItem
            link="/workshop"
            label="Workshop"
            isActive={path.includes("/workshop")}
            activeMenu={ActiveWorkshopMenu}
            inActiveMenu={InActiveWorkshopMenu}
          />
        </Box>
      </Box>
    </>
  );
}

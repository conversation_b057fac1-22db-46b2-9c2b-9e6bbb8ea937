import React, { useState, useEffect } from "react";
import ListLayout from "@/components/ListLayout";
import { useTranslation } from "react-i18next";
import { Avatar, Tooltip, Grid, Box, TextField, Button } from "@mui/material";
import { ReactComponent as RefreshIcon } from "@/assets/images/icon_refresh.svg";
import RingLoader from "react-spinners/RingLoader";
import DataTable from "@/components/DataTable";
import CommonUtil from "@/util/CommonUtils";
import { getOperationLog } from "@/services/log";
import IconHandaler from "../../components/IconHandaler";
import VisibilityIcon from "@mui/icons-material/Visibility";

import { REACT_OPERATION_LOG_DETAIL } from "@/router/ReactEndPoints";
import { useNavigate } from "react-router-dom";
import OpercationSearch from "./OpercationSearch";
function OperationLog() {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const navigate = useNavigate();
  const [totalRecords, setTotalRecords] = useState(0);
  const [filters, setFilters] = useState({
    pageNumber: 1,
    pageSize: 5,
    userName: "",
    ipAddr: "",
    location: "",
    status: "",
  });

  const defaultFilters = {
    pageNumber: 1,
    pageSize: 5,
    userName: "",
    ipAddr: "",
    location: "",
    status: "",
  };

  const columns = [
    {
      field: "method",
      headerName: `${t("operation_log.method")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip
          title={t(`operation_log.${e.row.method}`)}
          arrow
          placement="bottom">
          <span>
            {CommonUtil.formatLongText(
              t(`operation_log.${e.row.method}`) || "-"
            )}
          </span>
        </Tooltip>
      ),
    },
    {
      field: "userName",
      headerName: `${t("operation_log.account")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.userName} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.userName || "-")}</span>
        </Tooltip>
      ),
    },
    {
      field: "ipAddr",
      headerName: `${t("operation_log.ipAddr")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.ipAddr} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.ipAddr)}</span>
        </Tooltip>
      ),
    },
    // {
    //   field: "location",
    //   headerName: `${t("operation_log.address")}`,
    //   flex: 1,
    //   headerAlign: "center",
    //   align: "center",
    //   renderCell: (e) => (
    //     <Tooltip title={e.row.location} arrow placement="bottom">
    //       <span>{CommonUtil.formatLongText(e.row.location || "-")}</span>
    //     </Tooltip>
    //   ),
    // },
    {
      field: "operationTime",
      headerName: `${t("operation_log.operationTime")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.operationTime} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.operationTime || "-")}</span>
        </Tooltip>
      ),
    },
    {
      field: "status",
      headerName: `${t("operation_log.status")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip
          title={
            e.row.status == "0"
              ? t("common.common_success")
              : t("common.common_fail")
          }
          arrow
          placement="bottom">
          <span>
            {CommonUtil.formatLongText(
              e.row.status == "0"
                ? t("common.common_success")
                : t("common.common_fail") || "-"
            )}
          </span>
        </Tooltip>
      ),
    },

    {
      headerName: `${t("LVLRC0010")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <IconHandaler>
          <Tooltip title={t("LVLDB0020")} arrow>
            <VisibilityIcon
              onClick={() => {
                navigate(REACT_OPERATION_LOG_DETAIL, {
                  state: {
                    id: e.row.id,
                  },
                });
              }}
              style={{
                alignSelf: "center",
                paddingTop: "0px",
                paddingLeft: "5px",
                opacity: "1",
                fontSize: "20px",
                color: "#A2A3A3",
              }}
            />
          </Tooltip>
        </IconHandaler>
      ),
    },
  ];

  // 生成假数据
  const loadData = () => {
    let payload = {
      ...filters,
    };
    setIsLoading(true);
    getOperationLog(payload).then((res) => {
      setRecords(res?.data?.data?.objects);
      setTotalRecords(res?.data?.data?.totalCount);
    });

    setIsLoading(false);
  };

  // 在组件加载时生成假数据
  useEffect(() => {
    loadData();
  }, [filters]);

  const handlePageChange = (e) => {
    setFilters({
      ...filters,

      pageNumber: e + 1,
    });
  };

  const handlePageSize = (e) => {
    setFilters({
      ...defaultFilters,
      pageNumber: defaultFilters.pageNumber,
      pageSize: e,
    });
  };

  const handleSearch = (searchParams) => {
    setFilters({
      ...filters,
      ...searchParams,
      pageNumber: 1, // 重置页码
    });
  };

  const handleReset = () => {
    setFilters({
      ...defaultFilters,
    });
  };
  return (
    <React.Fragment>
      <ListLayout title={t("operation_log.title")}>
        <OpercationSearch onSearch={handleSearch} onReset={handleReset} />

        <Grid
          container
          xs={12}
          sx={{
            display: "flex",
            justifyContent: "flex-end",
          }}>
          <Grid item mt={4}>
            <RefreshIcon width={"35"} height={"35"} className="pointer" />
          </Grid>

          {isLoading ? (
            <RingLoader
              color={"#597ef7"}
              loading={isLoading}
              cssOverride={{
                display: "block",
                margin: "10% auto",
                borderColor: "#b37feb",
              }}
              size={60}
              speedMultiplier={3}
              aria-label="Loading Spinner"
              data-testid="loader"
            />
          ) : (
            <DataTable
              columns={columns}
              rows={records}
              page={filters.pageNumber - 1}
              totalRecords={totalRecords}
              rowsPerPage={filters.pageSize}
              onPageChange={(pn) => handlePageChange(pn)}
              onPageSizeChange={(ps) => handlePageSize(ps)}
              checkboxSelection={false}
            />
          )}
        </Grid>
      </ListLayout>
    </React.Fragment>
  );
}

export default OperationLog;
